{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Api",
      "runtimeExecutable": "npm",
      "runtimeArgs": ["run", "start:debug", "--", "--inspect-brk"],
      "sourceMaps": true,
      "cwd": "${workspaceRoot}",
      "envFile": "${workspaceFolder}/.env",
      "autoAttachChildProcesses": true,
      "restart": true,
      "stopOnEntry": false
    }
  ]
}
