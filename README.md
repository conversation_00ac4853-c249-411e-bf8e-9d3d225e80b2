# 1, <PERSON>ài đặt

## 1.1 Môi trường

- Cài đặt nvm và nodejs: [Hướng dẫn](https://www.freecodecamp.org/news/node-version-manager-nvm-install-guide/)
- Cài yarn khi cài xong nodejs + npm

```bash
npm i -g yarn
```

## 1.2 Build code

### Config

- Clone source code về máy: `git clone https://gitlab.com/audio_truyen/api.git`
- cd vào thư mục source code
- Tạo file .env từ file .env.example
- Sửa lại thông tin kết nối db, và các config khác trong file .env

### Chạy lệnh cài thư viện:

```bash
yarn install
```

### Chạy lệnh build source code

```bash
yarn build
```

## 1.3 Deploy

### Tạo folder upload source code lên server: https://194.127.192.237:8910/files

- Upload tất cả file lên folder, trừ folder src, node_modules không cần upload

### Tạo node project: https://194.127.192.237:8910/site/node

- Path: trỏ vào folder mới tạo và upload
- Run opt: chọn start:prod
- Port: Nhập port config trong .env
- Boot: checked

# 2, Update

- Khi sửa code thì sửa source trong src -> Chạy lại lệnh build -> Upload lại lên folder -> Restart lại service

# 3, API Documentation

## 3.1 User Management APIs

### 3.1.1 Soft Delete User

**Endpoint:** `DELETE /api/v1/users/:id`

**Mô tả:** API này thực hiện "soft delete" user bằng cách cập nhật các trường thông tin thêm suffix `_delete` thay vì xóa hoàn toàn khỏi database:

**Các trường được cập nhật:**

- `user_login` → `user_login_delete_{timestamp}`
- `user_nicename` → `user_nicename_delete_{timestamp}`
- `user_email` → `user_email_delete_{timestamp}`
- `display_name` → `display_name_delete_{timestamp}`
- `user_status` → `2` (đánh dấu đã deleted)

**Parameters:**

- `id` (path parameter): ID của user cần soft delete (số nguyên)

**Headers:**

- `Authorization: Bearer <token>` (nếu API yêu cầu authentication)

**Response:**

```json
{
  "data": {
    "message": "User with ID 123 has been successfully marked as deleted",
    "updatedUser": {
      "ID": 123,
      "user_login": "john_doe_delete_1626789123456",
      "user_nicename": "john_doe_delete_1626789123456",
      "user_email": "john@example.com_delete_1626789123456",
      "display_name": "John Doe_delete_1626789123456",
      "user_status": 2
    }
  },
  "message": "Request successful!!",
  "code": 200
}
```

**Error Responses:**

- `404 Not Found`: User không tồn tại
- `400 Bad Request`: User đã được soft delete trước đó
- `401 Unauthorized`: Không có quyền truy cập
- `500 Internal Server Error`: Lỗi server

**Ví dụ sử dụng:**

```bash
# Soft delete user có ID là 123
curl -X DELETE "http://localhost:3000/api/v1/users/123" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### 3.1.2 Restore User

**Endpoint:** `POST /api/v1/users/:id/restore`

**Mô tả:** API này khôi phục user đã bị soft delete bằng cách remove suffix `_delete` và reset user_status về 0:

**Các trường được khôi phục:**

- `user_login_delete_{timestamp}` → `user_login`
- `user_nicename_delete_{timestamp}` → `user_nicename`
- `user_email_delete_{timestamp}` → `user_email`
- `display_name_delete_{timestamp}` → `display_name`
- `user_status` → `0` (active)

**Parameters:**

- `id` (path parameter): ID của user cần restore (số nguyên)

**Response:**

```json
{
  "data": {
    "message": "User with ID 123 has been successfully restored",
    "restoredUser": {
      "ID": 123,
      "user_login": "john_doe",
      "user_nicename": "john_doe",
      "user_email": "<EMAIL>",
      "display_name": "John Doe",
      "user_status": 0
    }
  },
  "message": "Request successful!!",
  "code": 200
}
```

**Error Responses:**

- `404 Not Found`: User không tồn tại
- `400 Bad Request`: User chưa bị soft delete
- `401 Unauthorized`: Không có quyền truy cập

**Ví dụ sử dụng:**

```bash
# Restore user có ID là 123
curl -X POST "http://localhost:3000/api/v1/users/123/restore" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

**Ưu điểm của Soft Delete:**

- ✅ **An toàn:** Không mất dữ liệu vĩnh viễn
- ✅ **Có thể phục hồi:** Dữ liệu vẫn tồn tại trong database
- ✅ **Audit trail:** Có thể theo dõi được lịch sử
- ✅ **Tránh lỗi:** Không làm ảnh hưởng đến foreign key constraints

**Lưu ý quan trọng:**

- User đã được soft delete sẽ không thể đăng nhập
- Email và username sẽ được "giải phóng" cho user khác sử dụng
- Có thể restore user bằng API `/users/:id/restore`
- Sau khi restore, user có thể đăng nhập bình thường
