generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_AUDIORFULL_URL")
}

model mig_wpva_postmeta {
  meta_id    BigInt  @unique(map: "meta_id") @default(autoincrement()) @db.UnsignedBigInt
  post_id    BigInt  @default(0) @db.UnsignedBigInt
  meta_key   String  @db.VarChar(255)
  meta_value String? @db.LongText

  @@id([post_id, meta_key, meta_id])
  @@index([meta_key, meta_value(length: 32), post_id, meta_id], map: "meta_key")
  @@index([meta_value(length: 32), meta_id], map: "meta_value")
  @@map("_mig_wpva_postmeta")
}

model wp_pinterest_automatic {
  id     Int      @id @default(autoincrement())
  action String   @db.VarChar(50)
  data   String   @db.Text
  date   DateTime @default(now()) @db.Timestamp(0)
  camp   String   @db.<PERSON>har(20)
}

model wpva_actionscheduler_actions {
  action_id            BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  hook                 String
  status               String    @db.VarChar(20)
  scheduled_date_gmt   DateTime? @default(now()) @db.DateTime(0)
  scheduled_date_local DateTime? @default(now()) @db.DateTime(0)
  args                 String?
  schedule             String?   @db.LongText
  group_id             BigInt    @default(0) @db.UnsignedBigInt
  attempts             Int       @default(0)
  last_attempt_gmt     DateTime? @default(now()) @db.DateTime(0)
  last_attempt_local   DateTime? @default(now()) @db.DateTime(0)
  claim_id             BigInt    @default(0) @db.UnsignedBigInt
  extended_args        String?   @db.VarChar(8000)
  priority             Int       @default(10) @db.UnsignedTinyInt

  @@index([args], map: "args")
  @@index([claim_id], map: "claim_id")
  @@index([claim_id, status, scheduled_date_gmt], map: "claim_id_status_scheduled_date_gmt")
  @@index([group_id], map: "group_id")
  @@index([hook], map: "hook")
  @@index([last_attempt_gmt], map: "last_attempt_gmt")
  @@index([scheduled_date_gmt], map: "scheduled_date_gmt")
  @@index([status], map: "status")
}

model wpva_actionscheduler_claims {
  claim_id         BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  date_created_gmt DateTime @default(now()) @db.DateTime(0)

  @@index([date_created_gmt], map: "date_created_gmt")
}

model wpva_actionscheduler_groups {
  group_id BigInt @id @default(autoincrement()) @db.UnsignedBigInt
  slug     String @db.VarChar(255)

  @@index([slug(length: 191)], map: "slug")
}

model wpva_actionscheduler_logs {
  log_id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  action_id      BigInt    @db.UnsignedBigInt
  message        String    @db.Text
  log_date_gmt   DateTime? @default(now()) @db.DateTime(0)
  log_date_local DateTime? @default(now()) @db.DateTime(0)

  @@index([action_id], map: "action_id")
  @@index([log_date_gmt], map: "log_date_gmt")
}

model wpva_ai_statistics {
  id      BigInt    @id @default(autoincrement())
  block   Int       @db.UnsignedInt
  version Int       @db.UnsignedInt
  date    DateTime? @db.Date
  views   Int       @default(0) @db.UnsignedInt
  clicks  Int       @default(0) @db.UnsignedInt

  @@unique([block, version, date], map: "block_version")
}

model wpva_ajaxsearchpro {
  id   Int    @id @default(autoincrement())
  name String @db.Text
  data String @db.MediumText
}

model wpva_ajaxsearchpro_priorities {
  post_id  Int
  blog_id  Int
  priority Int

  @@id([post_id, blog_id])
  @@index([post_id, blog_id], map: "post_blog_id")
}

model wpva_ajaxsearchpro_statistics {
  id        Int    @id @default(autoincrement())
  search_id Int
  keyword   String @db.Text
  num       Int
  last_date Int
}

model wpva_asp_index {
  doc          BigInt @default(0) @db.UnsignedBigInt
  term         String @default("0") @db.VarChar(150)
  term_reverse String @default("0") @db.VarChar(150)
  blogid       Int    @default(0) @db.UnsignedMediumInt
  content      Int    @default(0) @db.UnsignedSmallInt
  title        Int    @default(0) @db.UnsignedTinyInt
  comment      Int    @default(0) @db.UnsignedTinyInt
  tag          Int    @default(0) @db.UnsignedTinyInt
  link         Int    @default(0) @db.UnsignedTinyInt
  author       Int    @default(0) @db.UnsignedTinyInt
  excerpt      Int    @default(0) @db.UnsignedTinyInt
  customfield  Int    @default(0) @db.UnsignedSmallInt
  post_type    String @default("post") @db.VarChar(50)
  lang         String @default("0") @db.VarChar(20)

  @@id([doc, term, blogid])
  @@index([term_reverse(length: 20), post_type(length: 20), blogid, lang(length: 10)], map: "rterm_ptype_bid_lang")
  @@index([term(length: 20), post_type(length: 20), blogid, lang(length: 10)], map: "term_ptype_bid_lang")
}

model wpva_asp_synonyms {
  id       Int    @id @default(autoincrement())
  keyword  String @db.VarChar(50)
  synonyms String @db.Text
  lang     String @db.VarChar(20)

  @@unique([keyword, lang], map: "keyword")
  @@index([keyword, lang], map: "keyword_lang")
}

model wpva_commentmeta {
  meta_id    BigInt  @unique(map: "meta_id") @default(autoincrement()) @db.UnsignedBigInt
  comment_id BigInt  @default(0) @db.UnsignedBigInt
  meta_key   String  @db.VarChar(255)
  meta_value String? @db.LongText

  @@id([meta_key, comment_id, meta_id])
  @@index([comment_id, meta_key, meta_value(length: 32)], map: "comment_id")
  @@index([meta_value(length: 32)], map: "meta_value")
}

model wpva_comments {
  comment_ID           BigInt   @unique(map: "comment_ID") @default(autoincrement()) @db.UnsignedBigInt
  comment_post_ID      BigInt   @default(0) @db.UnsignedBigInt
  comment_author       String   @db.TinyText
  comment_author_email String   @default("") @db.VarChar(100)
  comment_author_url   String   @default("") @db.VarChar(200)
  comment_author_IP    String   @default("") @db.VarChar(100)
  comment_date         DateTime @default(now()) @db.DateTime(0)
  comment_date_gmt     DateTime @default(now()) @db.DateTime(0)
  comment_content      String   @db.Text
  comment_karma        Int      @default(0)
  comment_approved     String   @default("1") @db.VarChar(20)
  comment_agent        String   @default("") @db.VarChar(255)
  comment_type         String   @default("comment") @db.VarChar(20)
  comment_parent       BigInt   @default(0) @db.UnsignedBigInt
  user_id              BigInt   @default(0) @db.UnsignedBigInt

  @@id([comment_post_ID, comment_ID])
  @@index([comment_approved, comment_date_gmt, comment_ID], map: "comment_approved_date_gmt")
  @@index([comment_author_email, comment_post_ID, comment_ID], map: "comment_author_email")
  @@index([comment_date_gmt, comment_ID], map: "comment_date_gmt")
  @@index([comment_parent, comment_ID], map: "comment_parent")
  @@index([comment_post_ID, comment_parent, comment_approved, comment_type, user_id, comment_date_gmt, comment_ID], map: "comment_post_parent_approved")
  @@index([comment_type], map: "woo_idx_comment_type")
}

model wpva_duplicator_pro_entities {
  id   Int     @id @default(autoincrement())
  type String? @db.VarChar(100)
  data String? @db.Text

  @@index([type], map: "type_idx")
}

model wpva_duplicator_pro_packages {
  id      BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  name    String   @db.VarChar(250)
  hash    String   @db.VarChar(50)
  status  Int
  created DateTime @default(now()) @db.DateTime(0)
  owner   String   @db.VarChar(60)
  package String   @db.LongText

  @@index([hash], map: "hash")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model wpva_e_submissions {
  id                      BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  type                    String?  @db.VarChar(60)
  hash_id                 String   @unique(map: "hash_id_unique_index") @db.VarChar(60)
  main_meta_id            BigInt   @db.UnsignedBigInt
  post_id                 BigInt   @db.UnsignedBigInt
  referer                 String   @db.VarChar(500)
  referer_title           String?  @db.VarChar(300)
  element_id              String   @db.VarChar(20)
  form_name               String   @db.VarChar(60)
  campaign_id             BigInt   @db.UnsignedBigInt
  user_id                 BigInt?  @db.UnsignedBigInt
  user_ip                 String   @db.VarChar(46)
  user_agent              String   @db.Text
  actions_count           Int?     @default(0)
  actions_succeeded_count Int?     @default(0)
  status                  String   @db.VarChar(20)
  is_read                 Boolean  @default(false)
  meta                    String?  @db.Text
  created_at_gmt          DateTime @db.DateTime(0)
  updated_at_gmt          DateTime @db.DateTime(0)
  created_at              DateTime @db.DateTime(0)
  updated_at              DateTime @db.DateTime(0)

  @@index([campaign_id], map: "campaign_id_index")
  @@index([created_at_gmt], map: "created_at_gmt_index")
  @@index([created_at], map: "created_at_index")
  @@index([element_id], map: "element_id_index")
  @@index([hash_id], map: "hash_id_index")
  @@index([is_read], map: "is_read_index")
  @@index([main_meta_id], map: "main_meta_id_index")
  @@index([post_id], map: "post_id_index")
  @@index([referer(length: 191)], map: "referer_index")
  @@index([referer_title(length: 191)], map: "referer_title_index")
  @@index([status], map: "status_index")
  @@index([type], map: "type_index")
  @@index([updated_at_gmt], map: "updated_at_gmt_index")
  @@index([updated_at], map: "updated_at_index")
  @@index([user_id], map: "user_id_index")
  @@index([user_ip], map: "user_ip_index")
}

model wpva_e_submissions_actions_log {
  id             BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  submission_id  BigInt   @db.UnsignedBigInt
  action_name    String   @db.VarChar(60)
  action_label   String?  @db.VarChar(60)
  status         String   @db.VarChar(20)
  log            String?  @db.Text
  created_at_gmt DateTime @db.DateTime(0)
  updated_at_gmt DateTime @db.DateTime(0)
  created_at     DateTime @db.DateTime(0)
  updated_at     DateTime @db.DateTime(0)

  @@index([action_name], map: "action_name_index")
  @@index([created_at_gmt], map: "created_at_gmt_index")
  @@index([created_at], map: "created_at_index")
  @@index([status], map: "status_index")
  @@index([submission_id], map: "submission_id_index")
  @@index([updated_at_gmt], map: "updated_at_gmt_index")
  @@index([updated_at], map: "updated_at_index")
}

model wpva_e_submissions_values {
  id            BigInt  @id @default(autoincrement()) @db.UnsignedBigInt
  submission_id BigInt  @default(0) @db.UnsignedBigInt
  key           String? @db.VarChar(60)
  value         String? @db.LongText

  @@index([key], map: "key_index")
  @@index([submission_id], map: "submission_id_index")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model wpva_ihc_cheat_off {
  uid  Int
  hash String @db.VarChar(40)

  @@ignore
}

model wpva_ihc_coupons {
  id                     Int      @id @default(autoincrement())
  code                   String?  @db.VarChar(200)
  settings               String?  @db.Text
  submited_coupons_count Int?
  status                 Boolean?
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model wpva_ihc_dashboard_notifications {
  type  String @db.VarChar(40)
  value Int?   @default(0)

  @@ignore
}

model wpva_ihc_debug_payments {
  id          Int       @id @default(autoincrement())
  source      String?   @db.VarChar(200)
  message     String?   @db.Text
  insert_time DateTime? @db.DateTime(0)
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model wpva_ihc_download_monitor_limit {
  uid            Int
  lid            Int
  download_limit Int

  @@ignore
}

model wpva_ihc_gift_templates {
  id       Int     @id @default(autoincrement())
  lid      Int?
  settings String? @db.Text
  status   Int?    @db.TinyInt
}

model wpva_ihc_invitation_codes {
  id           Int      @id @default(autoincrement())
  code         String?  @db.VarChar(200)
  settings     String?  @db.Text
  submited     Int?
  repeat_limit Int?
  status       Boolean?
}

model wpva_ihc_memberships {
  id                        Int                         @id @default(autoincrement())
  name                      String                      @db.VarChar(200)
  label                     String                      @db.VarChar(200)
  short_description         String?                     @db.VarChar(400)
  payment_type              String?                     @db.VarChar(50)
  price                     Decimal?                    @default(0.00) @db.Decimal(12, 2)
  status                    Boolean?                    @default(true)
  the_order                 Int?
  created_at                Int?

  @@index([id], map: "idx_ihc_memberships_id")
}

model wpva_ihc_memberships_meta {
  id                   Int                   @id @default(autoincrement())
  membership_id        Int
  meta_key             String                @db.VarChar(300)
  meta_value           String?               @db.Text

  @@index([membership_id], map: "idx_ihc_memberships_meta_membership_id")
}

model wpva_ihc_notifications {
  id                Int      @id @default(autoincrement())
  notification_type String?  @db.VarChar(200)
  level_id          String?  @db.VarChar(200)
  subject           String?  @db.Text
  message           String?  @db.Text
  pushover_message  String?  @db.Text
  pushover_status   Boolean  @default(false)
  status            Boolean?
}

model wpva_ihc_notifications_logs {
  id                Int      @id @default(autoincrement())
  notification_type String?  @db.VarChar(100)
  email_address     String?  @db.VarChar(300)
  subject           String?  @db.VarChar(300)
  message           String?  @db.Text
  uid               Int
  lid               Int
  create_date       DateTime @default(now()) @db.Timestamp(0)
}

model wpva_ihc_orders {
  id                Int      @id @default(autoincrement())
  uid               Int?
  lid               Int?
  amount_type       String?  @db.VarChar(200)
  amount_value      Decimal? @default(0.00) @db.Decimal(12, 2)
  automated_payment Boolean?
  status            String?  @db.VarChar(100)
  create_date       DateTime @default(now()) @db.Timestamp(0)

  wpva_ihc_orders_meta wpva_ihc_orders_meta[]

  @@index([uid], map: "idx_ihc_orders_uid")
}

model wpva_ihc_orders_meta {
  id              Int              @id @default(autoincrement())
  order_id        Int?
  meta_key        String?          @db.VarChar(200)
  meta_value      String?          @db.Text
  wpva_ihc_orders wpva_ihc_orders? @relation(fields: [order_id], references: [id])

  @@index([order_id], map: "idx_ihc_orders_meta_order_id")
}

model wpva_ihc_reason_for_cancel_delete_levels {
  id          Int     @id @default(autoincrement())
  uid         Int
  lid         Int
  reason      String? @db.VarChar(400)
  action_type String? @db.VarChar(30)
  action_date Int?
}

model wpva_ihc_security_login {
  id             Int      @id @default(autoincrement())
  username       String?  @db.VarChar(200)
  ip             String?  @db.VarChar(30)
  log_time       Int?
  attempts_count Int?
  locked         Boolean?
}

model wpva_ihc_taxes {
  id           Int      @id @default(autoincrement())
  country_code String?  @db.VarChar(20)
  state_code   String?  @default("") @db.VarChar(50)
  amount_value Decimal? @default(0.00) @db.Decimal(12, 2)
  label        String?  @db.VarChar(200)
  description  String?  @db.Text
  status       Boolean?
}

model wpva_ihc_user_levels {
  id           Int       @id @default(autoincrement())
  user_id      Int
  level_id     Int
  start_time   DateTime? @db.DateTime(0)
  update_time  DateTime? @db.DateTime(0)
  expire_time  DateTime? @db.DateTime(0)
  notification Boolean?  @default(false)
  status       Int

  @@index([user_id], map: "idx_ihc_user_levels_user_id")
}

model wpva_ihc_user_logs {
  id          Int     @id @default(autoincrement())
  uid         Int     @default(0)
  lid         Int?
  log_type    String? @db.VarChar(100)
  log_content String? @db.Text
  create_date Int?

  @@index([uid], map: "idx_ihc_user_logs_uid")
}

model wpva_ihc_user_sites {
  id      Int  @id @default(autoincrement())
  site_id Int?
  uid     Int?
  lid     Int?
}

model wpva_ihc_user_subscriptions_meta {
  id              BigInt  @id @default(autoincrement())
  subscription_id BigInt
  meta_key        String  @db.VarChar(300)
  meta_value      String? @db.Text

  @@index([subscription_id], map: "idx_ihc_user_subscriptions_meta_subscription_id")
}

model wpva_ihc_woo_product_level_relations {
  id                 Int     @id @default(autoincrement())
  ihc_woo_product_id Int?
  lid                Int?
  woo_item           Int?
  woo_item_type      String? @db.VarChar(200)
}

model wpva_ihc_woo_products {
  id             Int       @id @default(autoincrement())
  slug           String    @db.VarChar(200)
  discount_type  String?   @db.VarChar(20)
  discount_value Decimal?  @db.Decimal(12, 2)
  start_date     DateTime? @db.DateTime(0)
  end_date       DateTime? @db.DateTime(0)
  settings       String?   @db.Text
  status         Boolean?  @default(false)
}

model wpva_indeed_members_payments {
  id           Int      @id @default(autoincrement())
  txn_id       String?  @db.VarChar(100)
  u_id         Int?
  payment_data String?  @db.Text
  history      String?  @db.Text
  orders       String?  @db.Text
  paydate      DateTime @default(now()) @db.Timestamp(0)

  @@index([u_id], map: "idx_indeed_members_payments_uid")
}

model wpva_kdn_urls {
  id                 BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  post_id            BigInt
  url                String    @db.VarChar(2560)
  last_url           String?   @db.VarChar(2560)
  thumbnail_url      String?   @db.VarChar(2560)
  category_id        Int       @db.MediumInt
  is_saved           Boolean   @default(false)
  saved_post_id      BigInt?   @db.UnsignedBigInt
  last_saved_post_id BigInt?   @db.UnsignedBigInt
  update_count       Int       @default(0) @db.UnsignedInt
  is_locked          Int       @default(0) @db.TinyInt
  created_at         DateTime? @default(now()) @db.DateTime(0)
  updated_at         DateTime? @default(now()) @db.DateTime(0)
  saved_at           DateTime? @db.DateTime(0)
  recrawled_at       DateTime? @db.DateTime(0)
  deleted_at         DateTime? @db.DateTime(0)
  cache_process      BigInt?   @default(0)
  cache_content      String?   @db.LongText
  stop_crawling_all  String?   @db.VarChar(2560)
  bypass_delete      Int       @default(0) @db.TinyInt
}

model wpva_links {
  link_id          BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  link_url         String   @default("") @db.VarChar(255)
  link_name        String   @default("") @db.VarChar(255)
  link_image       String   @default("") @db.VarChar(255)
  link_target      String   @default("") @db.VarChar(25)
  link_description String   @default("") @db.VarChar(255)
  link_visible     String   @default("Y") @db.VarChar(20)
  link_owner       BigInt   @default(1) @db.UnsignedBigInt
  link_rating      Int      @default(0)
  link_updated     DateTime @default(now()) @db.DateTime(0)
  link_rel         String   @default("") @db.VarChar(255)
  link_notes       String   @db.MediumText
  link_rss         String   @default("") @db.VarChar(255)

  @@index([link_visible], map: "link_visible")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model wpva_litespeed_crawler {
  id     BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  url    String   @default("") @db.VarChar(1000)
  res    String   @default("") @db.VarChar(255)
  reason String   @db.Text
  mtime  DateTime @default(now()) @db.Timestamp(0)

  @@index([res], map: "res")
  @@index([url(length: 191)], map: "url")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model wpva_litespeed_crawler_blacklist {
  id     BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  url    String   @default("") @db.VarChar(1000)
  res    String   @default("") @db.VarChar(255)
  reason String   @db.Text
  mtime  DateTime @default(now()) @db.Timestamp(0)

  @@index([res], map: "res")
  @@index([url(length: 191)], map: "url")
}

model wpva_litespeed_img_optm {
  id              BigInt @id @default(autoincrement()) @db.UnsignedBigInt
  post_id         BigInt @default(0) @db.UnsignedBigInt
  optm_status     Int    @default(0) @db.TinyInt
  src             String @db.Text
  src_filesize    Int    @default(0)
  target_filesize Int    @default(0)
  webp_filesize   Int    @default(0)

  @@index([optm_status], map: "optm_status")
  @@index([post_id], map: "post_id")
}

model wpva_litespeed_img_optming {
  id          BigInt @id @default(autoincrement()) @db.UnsignedBigInt
  post_id     BigInt @default(0) @db.UnsignedBigInt
  optm_status Int    @default(0) @db.TinyInt
  src         String @default("") @db.VarChar(1000)
  server_info String @db.Text

  @@index([optm_status], map: "optm_status")
  @@index([post_id], map: "post_id")
  @@index([src(length: 191)], map: "src")
}

model wpva_litespeed_url {
  id         BigInt @id @default(autoincrement())
  url        String @unique(map: "url", length: 191) @db.VarChar(500)
  cache_tags String @default("") @db.VarChar(1000)

  @@index([cache_tags(length: 191)], map: "cache_tags")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model wpva_litespeed_url_file {
  id       BigInt @id @default(autoincrement())
  url_id   BigInt
  vary     String @default("") @db.VarChar(32)
  filename String @default("") @db.VarChar(32)
  type     Int    @db.TinyInt
  expired  Int    @default(0)
  mobile   Int    @db.TinyInt
  webp     Int    @db.TinyInt

  @@index([filename], map: "filename")
  @@index([filename, expired], map: "filename_2")
  @@index([type], map: "type")
  @@index([url_id, expired], map: "url_id")
  @@index([url_id, vary, type], map: "url_id_2")
}

model wpva_map_ad {
  id      Int     @id @default(autoincrement()) @db.UnsignedInt
  title   String  @db.VarChar(100)
  options String? @db.LongText
}

model wpva_map_media {
  id          Int     @id @default(autoincrement()) @db.UnsignedInt
  options     String? @db.LongText
  order_id    Int?    @db.UnsignedInt
  playlist_id Int     @db.UnsignedInt

  @@index([playlist_id], map: "playlist_id")
}

model wpva_map_media_taxonomy {
  id          Int  @id @default(autoincrement()) @db.UnsignedInt
  media_id    Int? @db.UnsignedInt
  playlist_id Int? @db.UnsignedInt
  taxonomy_id Int? @db.UnsignedInt

  @@index([taxonomy_id], map: "taxonomy_id")
}

model wpva_map_players {
  id         Int     @id @default(autoincrement()) @db.UnsignedInt
  title      String  @db.VarChar(100)
  preset     String  @db.VarChar(25)
  options    String? @db.LongText
  custom_css String? @db.LongText
  custom_js  String? @db.LongText
}

model wpva_map_playlist_taxonomy {
  id          Int  @id @default(autoincrement()) @db.UnsignedInt
  playlist_id Int? @db.UnsignedInt
  taxonomy_id Int? @db.UnsignedInt

  @@index([taxonomy_id], map: "taxonomy_id")
}

model wpva_map_playlists {
  id      Int     @id @default(autoincrement()) @db.UnsignedInt
  title   String  @db.VarChar(100)
  options String? @db.Text
}

model wpva_map_statistics {
  id                   Int       @id @default(autoincrement()) @db.UnsignedInt
  origtype             String?   @db.VarChar(15)
  title                String?   @db.VarChar(300)
  artist               String?   @db.VarChar(100)
  album                String?   @db.VarChar(100)
  thumb                String?   @db.VarChar(300)
  audio_url            String?   @db.VarChar(300)
  c_play               Int?      @db.UnsignedInt
  c_time               Int?      @db.UnsignedInt
  c_like               Int?      @db.UnsignedInt
  c_download           Int?
  c_finish             Int?      @db.UnsignedInt
  skipped_first_minute Int?      @default(0) @db.UnsignedInt
  c_date               DateTime? @db.Date
  c_user_ip            String?   @db.VarChar(50)
  media_id             Int?      @db.UnsignedInt
  playlist_id          Int?      @db.UnsignedInt
  player_id            Int?      @db.UnsignedInt

  @@index([media_id], map: "media_id")
}

model wpva_map_statistics_country {
  id           Int     @id @default(autoincrement()) @db.UnsignedInt
  country      String? @db.VarChar(100)
  country_code String? @db.VarChar(10)
  continent    String? @db.VarChar(15)
}

model wpva_map_statistics_country_play {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  title       String?   @db.VarChar(300)
  artist      String?   @db.VarChar(100)
  album       String?   @db.VarChar(100)
  thumb       String?   @db.VarChar(300)
  audio_url   String?   @db.VarChar(300)
  c_play      Int?      @db.UnsignedInt
  c_time      Int?      @db.UnsignedInt
  c_date      DateTime? @db.Date
  media_id    Int?      @db.UnsignedInt
  playlist_id Int?      @db.UnsignedInt
  player_id   Int?      @db.UnsignedInt
  country_id  Int?      @db.UnsignedInt

  @@index([media_id], map: "media_id")
}

model wpva_map_statistics_user {
  id                Int     @id @default(autoincrement()) @db.UnsignedInt
  user_id           Int?    @db.UnsignedSmallInt
  user_display_name String? @db.VarChar(100)
  user_role         String? @db.VarChar(50)

  @@index([user_id], map: "user_id")
}

model wpva_map_statistics_user_play {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  title       String?   @db.VarChar(300)
  artist      String?   @db.VarChar(100)
  album       String?   @db.VarChar(100)
  thumb       String?   @db.VarChar(300)
  audio_url   String?   @db.VarChar(300)
  c_play      Int?      @db.UnsignedInt
  c_time      Int?      @db.UnsignedInt
  c_date      DateTime? @db.Date
  media_id    Int?      @db.UnsignedInt
  playlist_id Int?      @db.UnsignedInt
  player_id   Int?      @db.UnsignedInt
  user_id     Int?      @db.UnsignedInt

  @@index([media_id], map: "media_id")
}

model wpva_map_taxonomy {
  id          Int     @id @default(autoincrement()) @db.UnsignedInt
  type        String? @db.VarChar(15)
  title       String? @db.VarChar(100)
  description String? @db.VarChar(500)
}

model wpva_mvp_ad {
  id          Int     @id @default(autoincrement()) @db.UnsignedInt
  options     String? @db.LongText
  media_id    Int?    @db.UnsignedInt
  playlist_id Int?    @db.UnsignedInt
  ad_id       Int?    @db.UnsignedInt

  @@index([media_id], map: "media_id")
}

model wpva_mvp_annotation {
  id          Int     @id @default(autoincrement()) @db.UnsignedInt
  options     String? @db.LongText
  media_id    Int?    @db.UnsignedInt
  playlist_id Int?    @db.UnsignedInt
  ad_id       Int?    @db.UnsignedInt

  @@index([media_id], map: "media_id")
}

model wpva_mvp_favorites {
  id          Int  @id @default(autoincrement()) @db.UnsignedInt
  user_id     Int? @db.UnsignedInt
  playlist_id Int? @db.UnsignedInt
  media_id    Int? @db.UnsignedInt

  @@index([media_id], map: "media_id")
}

model wpva_mvp_global_ad {
  id      Int     @id @default(autoincrement()) @db.UnsignedInt
  title   String  @db.VarChar(100)
  options String? @db.LongText
}

model wpva_mvp_media {
  id          Int     @id @default(autoincrement()) @db.UnsignedInt
  title       String? @db.VarChar(300)
  options     String? @db.LongText
  order_id    Int?    @db.UnsignedInt
  playlist_id Int     @db.UnsignedInt
  disabled    Int?    @default(0) @db.TinyInt
  user_id     Int?    @db.UnsignedInt

  @@index([playlist_id], map: "playlist_id")
}

model wpva_mvp_path {
  id          Int     @id @default(autoincrement()) @db.UnsignedInt
  options     String? @db.LongText
  media_id    Int     @db.UnsignedInt
  playlist_id Int?    @db.UnsignedInt

  @@index([media_id], map: "media_id")
}

model wpva_mvp_players {
  id         Int     @id @default(autoincrement()) @db.UnsignedInt
  title      String  @db.VarChar(100)
  preset     String  @db.VarChar(50)
  options    String? @db.Text
  custom_css String? @db.LongText
  custom_js  String? @db.LongText
}

model wpva_mvp_playlists {
  id           Int     @id @default(autoincrement()) @db.UnsignedInt
  title        String  @db.VarChar(100)
  options      String? @db.Text
  user_id      Int?    @db.UnsignedInt
  is_edit      Int?    @default(0) @db.TinyInt
  edit_user_id Int?    @db.UnsignedTinyInt
}

model wpva_mvp_settings {
  id      Int     @id @db.TinyInt
  options String? @db.Text
}

model wpva_mvp_statistics {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  origtype    String?   @db.VarChar(20)
  title       String?   @db.VarChar(300)
  c_play      Int?      @default(0) @db.UnsignedInt
  c_time      Int?      @default(0) @db.UnsignedInt
  c_download  Int?      @default(0)
  c_finish    Int?      @default(0) @db.UnsignedInt
  c_date      DateTime? @db.Date
  media_id    Int       @db.UnsignedInt
  playlist_id Int?      @db.UnsignedInt
  user_id     Int?      @db.UnsignedInt

  @@index([media_id], map: "media_id")
}

model wpva_mvp_statistics_country {
  id           Int     @id @default(autoincrement()) @db.UnsignedInt
  country      String? @db.VarChar(100)
  country_code String? @db.VarChar(10)
  continent    String? @db.VarChar(15)
}

model wpva_mvp_statistics_country_play {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  title       String?   @db.VarChar(300)
  thumb       String?   @db.VarChar(300)
  video_url   String?   @db.VarChar(300)
  c_play      Int?      @db.UnsignedInt
  c_time      Int?      @db.UnsignedInt
  c_date      DateTime? @db.Date
  media_id    Int?      @db.UnsignedInt
  playlist_id Int?      @db.UnsignedInt
  country_id  Int?      @db.UnsignedInt

  @@index([media_id], map: "media_id")
}

model wpva_mvp_statistics_user {
  id                Int     @id @default(autoincrement()) @db.UnsignedInt
  user_id           Int?    @db.UnsignedSmallInt
  user_display_name String? @db.VarChar(100)
  user_role         String? @db.VarChar(50)

  @@index([user_id], map: "user_id")
}

model wpva_mvp_statistics_user_play {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  title       String?   @db.VarChar(300)
  thumb       String?   @db.VarChar(300)
  video_url   String?   @db.VarChar(300)
  c_play      Int?      @db.UnsignedInt
  c_time      Int?      @db.UnsignedInt
  c_date      DateTime? @db.Date
  media_id    Int?      @db.UnsignedInt
  playlist_id Int?      @db.UnsignedInt
  user_id     Int?      @db.UnsignedInt

  @@index([media_id], map: "media_id")
}

model wpva_mvp_subtitle {
  id          Int     @id @default(autoincrement()) @db.UnsignedInt
  options     String? @db.LongText
  media_id    Int     @db.UnsignedInt
  playlist_id Int?    @db.UnsignedInt

  @@index([media_id], map: "media_id")
}

model wpva_mvp_watched_percentage {
  id          Int     @id @default(autoincrement()) @db.UnsignedInt
  title       String? @db.VarChar(300)
  watched     Int?    @db.UnsignedMediumInt
  duration    Int?    @db.UnsignedMediumInt
  media_id    Int?    @db.UnsignedInt
  playlist_id Int?    @db.UnsignedInt
  user_id     Int?    @db.UnsignedInt

  @@index([media_id], map: "media_id")
}

model wpva_options {
  option_id    BigInt @unique(map: "option_id") @default(autoincrement()) @db.UnsignedBigInt
  option_name  String @id @default("")
  option_value String @db.LongText
  autoload     String @default("yes") @db.VarChar(20)

  @@index([autoload], map: "autoload")
}

model wpva_popularpostsdata {
  postid      BigInt   @id
  day         DateTime @db.DateTime(0)
  last_viewed DateTime @db.DateTime(0)
  pageviews   BigInt?  @default(1)
}

model wpva_popularpostssummary {
  ID            BigInt   @id @default(autoincrement())
  postid        BigInt
  pageviews     BigInt   @default(1)
  view_date     DateTime @db.Date
  view_datetime DateTime @db.DateTime(0)

  @@index([postid], map: "postid")
  @@index([view_date], map: "view_date")
  @@index([view_datetime], map: "view_datetime")
}

model wpva_popularpoststransients {
  ID        BigInt   @id @default(autoincrement())
  tkey      String
  tkey_date DateTime @db.DateTime(0)
}

model wpva_postmeta {
  meta_id    BigInt  @unique(map: "meta_id") @default(autoincrement()) @db.UnsignedBigInt
  post_id    BigInt  @default(0) @db.UnsignedBigInt
  meta_key   String  @db.VarChar(255)
  meta_value String? @db.LongText

  @@id([post_id, meta_key, meta_id])
  @@index([meta_key, meta_value(length: 32), post_id, meta_id], map: "meta_key")
  @@index([meta_value(length: 32), meta_id], map: "meta_value")
}

model wpva_posts {
  ID                    BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  post_author           BigInt   @default(0) @db.UnsignedBigInt
  post_date             DateTime @default(now()) @db.DateTime(0)
  post_date_gmt         DateTime @default(now()) @db.DateTime(0)
  post_content          String   @db.LongText
  post_title            String   @db.Text
  post_excerpt          String   @db.Text
  post_status           String   @default("publish") @db.VarChar(20)
  comment_status        String   @default("open") @db.VarChar(20)
  ping_status           String   @default("open") @db.VarChar(20)
  post_password         String   @default("") @db.VarChar(255)
  post_name             String   @default("") @db.VarChar(200)
  to_ping               String   @db.Text
  pinged                String   @db.Text
  post_modified         DateTime @default(now()) @db.DateTime(0)
  post_modified_gmt     DateTime @default(now()) @db.DateTime(0)
  post_content_filtered String   @db.LongText
  post_parent           BigInt   @default(0) @db.UnsignedBigInt
  guid                  String   @default("") @db.VarChar(255)
  menu_order            Int      @default(0)
  post_type             String   @default("post") @db.VarChar(20)
  post_mime_type        String   @default("") @db.VarChar(100)
  comment_count         BigInt   @default(0)

  @@index([post_author, post_type, post_status, post_date], map: "post_author")
  @@index([post_name], map: "post_name")
  @@index([post_parent, post_type, post_status], map: "post_parent")
  @@index([post_type, post_status, post_date, post_author], map: "type_status_date")
}

model wpva_rank_math_analytics_ga {
  id        BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  page      String   @db.VarChar(500)
  created   DateTime @default(now()) @db.Timestamp(0)
  pageviews Int      @db.MediumInt
  visitors  Int      @db.MediumInt

  @@index([page(length: 190)], map: "analytics_object_analytics")
}

model wpva_rank_math_analytics_gsc {
  id          BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  created     DateTime @default(now()) @db.Timestamp(0)
  query       String   @db.VarChar(1000)
  page        String   @db.VarChar(500)
  clicks      Int      @db.MediumInt
  impressions Int      @db.MediumInt
  position    Float
  ctr         Float

  @@index([page(length: 190)], map: "analytics_page")
  @@index([query(length: 190)], map: "analytics_query")
  @@index([clicks], map: "clicks")
  @@index([position], map: "rank_position")
}

model wpva_rank_math_analytics_inspections {
  id                       BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  page                     String   @db.VarChar(500)
  created                  DateTime @default(now()) @db.Timestamp(0)
  index_verdict            String   @db.VarChar(64)
  indexing_state           String   @db.VarChar(64)
  coverage_state           String   @db.Text
  page_fetch_state         String   @db.VarChar(64)
  robots_txt_state         String   @db.VarChar(64)
  mobile_usability_verdict String   @db.VarChar(64)
  mobile_usability_issues  String   @db.LongText
  rich_results_verdict     String   @db.VarChar(64)
  rich_results_items       String   @db.LongText
  last_crawl_time          DateTime @default(now()) @db.Timestamp(0)
  crawled_as               String   @db.VarChar(64)
  google_canonical         String   @db.Text
  user_canonical           String   @db.Text
  sitemap                  String   @db.Text
  referring_urls           String   @db.LongText
  raw_api_response         String   @db.LongText

  @@index([page(length: 190)], map: "analytics_object_page")
  @@index([created], map: "created")
  @@index([index_verdict], map: "index_verdict")
  @@index([mobile_usability_verdict], map: "mobile_usability_verdict")
  @@index([page_fetch_state], map: "page_fetch_state")
  @@index([rich_results_verdict], map: "rich_results_verdict")
  @@index([robots_txt_state], map: "robots_txt_state")
}

model wpva_rank_math_analytics_keyword_manager {
  id         BigInt  @id @default(autoincrement()) @db.UnsignedBigInt
  keyword    String  @db.VarChar(1000)
  collection String? @db.VarChar(200)
  is_active  Boolean @default(true)
}

model wpva_rank_math_analytics_objects {
  id                  BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  created             DateTime @default(now()) @db.Timestamp(0)
  title               String   @db.Text
  page                String   @db.VarChar(500)
  object_type         String   @db.VarChar(100)
  object_subtype      String   @db.VarChar(100)
  object_id           BigInt   @db.UnsignedBigInt
  primary_key         String   @db.VarChar(255)
  seo_score           Int      @default(0) @db.TinyInt
  page_score          Int      @default(0) @db.TinyInt
  is_indexable        Boolean  @default(true)
  schemas_in_use      String?  @db.VarChar(500)
  desktop_interactive Float?   @default(0)
  desktop_pagescore   Float?   @default(0)
  mobile_interactive  Float?   @default(0)
  mobile_pagescore    Float?   @default(0)
  pagespeed_refreshed DateTime @default(now()) @db.Timestamp(0)

  @@index([page(length: 190)], map: "analytics_object_page")
}

model wpva_rank_math_internal_links {
  id             BigInt @id @default(autoincrement()) @db.UnsignedBigInt
  url            String @db.VarChar(255)
  post_id        BigInt @db.UnsignedBigInt
  target_post_id BigInt @db.UnsignedBigInt
  type           String @db.VarChar(8)

  @@index([post_id, type], map: "link_direction")
}

model wpva_rank_math_internal_meta {
  object_id           BigInt @id @db.UnsignedBigInt
  internal_link_count Int?   @default(0) @db.UnsignedInt
  external_link_count Int?   @default(0) @db.UnsignedInt
  incoming_link_count Int?   @default(0) @db.UnsignedInt
}

model wpva_rank_math_redirections {
  id            BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  sources       String   @db.Text
  url_to        String   @db.Text
  header_code   Int      @db.UnsignedSmallInt
  hits          BigInt   @default(0) @db.UnsignedBigInt
  status        String   @default("active") @db.VarChar(25)
  created       DateTime @default(now()) @db.DateTime(0)
  updated       DateTime @default(now()) @db.DateTime(0)
  last_accessed DateTime @default(now()) @db.DateTime(0)

  @@index([status], map: "status")
}

model wpva_rank_math_redirections_cache {
  id             BigInt  @id @default(autoincrement()) @db.UnsignedBigInt
  from_url       String  @db.Text
  redirection_id BigInt  @db.UnsignedBigInt
  object_id      BigInt  @default(0) @db.UnsignedBigInt
  object_type    String  @default("post") @db.VarChar(10)
  is_redirected  Boolean @default(false)

  @@index([redirection_id], map: "redirection_id")
}

model wpva_search_filter_cache {
  id               BigInt  @id @default(autoincrement())
  post_id          BigInt
  post_parent_id   BigInt
  field_name       String  @db.VarChar(255)
  field_value      String  @db.VarChar(255)
  field_value_num  BigInt?
  field_parent_num BigInt?
  term_parent_id   BigInt?

  @@index([field_name(length: 32)], map: "sf_c_field_name_index")
  @@index([field_value(length: 32)], map: "sf_c_field_value_index")
  @@index([field_value_num], map: "sf_c_field_value_num_index")
}

model wpva_search_filter_term_results {
  id              BigInt  @id @default(autoincrement())
  field_name      String  @db.VarChar(255)
  field_value     String  @db.VarChar(255)
  field_value_num BigInt?
  result_ids      String  @db.MediumText

  @@index([field_name(length: 32)], map: "sf_tr_field_name_index")
  @@index([field_value(length: 32)], map: "sf_tr_field_value_index")
  @@index([field_value_num], map: "sf_tr_field_value_num_index")
}

model wpva_term_relationships {
  object_id        BigInt @default(0) @db.UnsignedBigInt
  term_taxonomy_id BigInt @default(0) @db.UnsignedBigInt
  term_order       Int    @default(0)

  @@id([object_id, term_taxonomy_id])
  @@index([term_taxonomy_id], map: "term_taxonomy_id")
}

model wpva_term_taxonomy {
  term_taxonomy_id BigInt @id @default(autoincrement()) @db.UnsignedBigInt
  term_id          BigInt @default(0) @db.UnsignedBigInt
  taxonomy         String @default("") @db.VarChar(32)
  description      String @db.LongText
  parent           BigInt @default(0) @db.UnsignedBigInt
  count            BigInt @default(0)

  @@unique([term_id, taxonomy], map: "term_id_taxonomy")
  @@index([taxonomy], map: "taxonomy")
}

model wpva_termmeta {
  meta_id    BigInt  @unique(map: "meta_id") @default(autoincrement()) @db.UnsignedBigInt
  term_id    BigInt  @default(0) @db.UnsignedBigInt
  meta_key   String  @db.VarChar(255)
  meta_value String? @db.LongText

  @@id([term_id, meta_key, meta_id])
  @@index([meta_key, meta_value(length: 32), term_id, meta_id], map: "meta_key")
  @@index([meta_value(length: 32), meta_id], map: "meta_value")
}

model wpva_terms {
  term_id    BigInt @id @default(autoincrement()) @db.UnsignedBigInt
  name       String @default("") @db.VarChar(200)
  slug       String @default("") @db.VarChar(200)
  term_group BigInt @default(0)

  @@index([name(length: 191)], map: "name")
  @@index([slug(length: 191)], map: "slug")
}

model wpva_usermeta {
  umeta_id   BigInt  @unique(map: "umeta_id") @default(autoincrement()) @db.UnsignedBigInt
  user_id    BigInt  @default(0) @db.UnsignedBigInt
  meta_key   String  @db.VarChar(255)
  meta_value String? @db.LongText

  @@id([user_id, meta_key, umeta_id])
  @@index([meta_key, meta_value(length: 32), user_id, umeta_id], map: "meta_key")
  @@index([meta_value(length: 32), umeta_id], map: "meta_value")
}

model wpva_users {
  ID                  BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  user_login          String   @default("") @db.VarChar(60)
  user_pass           String   @default("") @db.VarChar(255)
  user_nicename       String   @default("") @db.VarChar(50)
  user_email          String   @default("") @db.VarChar(100)
  user_url            String   @default("") @db.VarChar(100)
  user_registered     DateTime @default(now()) @db.DateTime(0)
  user_activation_key String   @default("") @db.VarChar(255)
  user_status         Int      @default(0)
  display_name        String   @default("") @db.VarChar(250)

  @@index([display_name], map: "display_name")
  @@index([user_email], map: "user_email")
  @@index([user_login], map: "user_login_key")
  @@index([user_nicename], map: "user_nicename")
}

model wpva_wpcc_urls {
  id            BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  post_id       BigInt
  url           String    @db.VarChar(2560)
  thumbnail_url String?   @db.VarChar(2560)
  category_id   Int       @db.MediumInt
  is_saved      Boolean   @default(false)
  saved_post_id BigInt?   @db.UnsignedBigInt
  update_count  Int       @default(0) @db.UnsignedInt
  is_locked     Int       @default(0) @db.TinyInt
  created_at    DateTime? @default(now()) @db.DateTime(0)
  updated_at    DateTime? @default(now()) @db.DateTime(0)
  saved_at      DateTime? @db.DateTime(0)
  recrawled_at  DateTime? @db.DateTime(0)
  deleted_at    DateTime? @db.DateTime(0)
}

model wpva_wpdm_social_shares {
  ID         Int    @id @default(autoincrement())
  post_id    Int    @default(0)
  url        String @default("") @db.Text
  channel    String @default("") @db.VarChar(255)
  user_id    Int    @default(0)
  user_agent String @default("") @db.Text
}

model wpva_wpie_template {
  id               Int      @id @default(autoincrement())
  status           String?  @db.VarChar(25)
  opration         String   @db.VarChar(100)
  username         String   @db.VarChar(60)
  unique_id        String   @db.VarChar(100)
  opration_type    String   @db.VarChar(100)
  options          String?  @db.LongText
  process_log      String?  @db.VarChar(255)
  process_lock     Int?
  create_date      DateTime @db.DateTime(0)
  last_update_date DateTime @db.DateTime(0)
}

model wpva_wpmailsmtp_debug_events {
  id         Int      @id @default(autoincrement()) @db.UnsignedInt
  content    String?  @db.Text
  initiator  String?  @db.Text
  event_type Int      @default(0) @db.UnsignedTinyInt
  created_at DateTime @default(now()) @db.Timestamp(0)
}

model wpva_wpmailsmtp_tasks_meta {
  id     BigInt   @id @default(autoincrement())
  action String   @db.VarChar(255)
  data   String   @db.LongText
  date   DateTime @db.DateTime(0)
}
