import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  HttpStatus,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  PaginatedResponse,
  Paging,
} from 'src/interfaces/response-format.interface';

export interface ResponseFormat<T> {
  data: T;
  message: string;
  paging?: Paging;
}

@Injectable()
export class ResponseFormatInterceptor<T>
  implements NestInterceptor<T, ResponseFormat<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler<T>,
  ): Observable<ResponseFormat<T>> {
    return next.handle().pipe(
      map((data) => {
        const response = {
          data: (data as PaginatedResponse<T>).items || data,
          message: 'Request successful!!',
          code: HttpStatus.OK,
        } as ResponseFormat<T>;

        if ((data as PaginatedResponse<T>).paging) {
          response.paging = (data as PaginatedResponse<T>).paging;
        }

        return response;
      }),
    );
  }
}

export function UseResponseFormat() {
  return function (target: any, key: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    descriptor.value = async function (...args: any[]) {
      const result = await originalMethod.apply(this, args);
      const response = {
        data: result.items || result,
        message: 'Operation successful',
      } as ResponseFormat<any>;

      if (result.paging) {
        response.paging = result.paging;
      }

      return response;
    };
    return descriptor;
  };
}
