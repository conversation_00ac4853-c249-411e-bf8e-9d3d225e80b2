import { ApiProperty } from '@nestjs/swagger';

class RatingFilter {
  @ApiProperty({
    description: 'Đánh giá từ',
    minimum: 1,
  })
  ratingFrom: number;

  @ApiProperty({
    description: '<PERSON><PERSON>h giá đến',
    minimum: 1,
  })
  ratingTo: number;
}

class EpisodeFilter {
  @ApiProperty({
    description: 'Số tập từ',
    minimum: 1,
  })
  episodeFrom: number;

  @ApiProperty({
    description: 'Số tập đến',
    minimum: 1,
  })
  episodeTo: number;
}
class NumberViewFilter {
  @ApiProperty({
    description: 'Lượt nghe từ',
    minimum: 1,
  })
  viewForm: number;

  @ApiProperty({
    description: 'Đến lượt nghe',
    minimum: 1,
  })
  viewTo: number;
}

export class FindStoryConditionQueryDto {
  @ApiProperty({
    description: 'Thể loại truyện',
    isArray: true,
    type: Number,
  })
  genres: number[];

  @ApiProperty({
    description: '<PERSON>ạ<PERSON> truyện',
    isArray: true,
    type: Number,
  })
  storyType: number[];

  @ApiProperty({
    description: 'Trạng thái',
    isArray: true,
    type: Number,
  })
  status: number[];

  @ApiProperty({
    description: 'Server',
    type: Number,
    isArray: true,
  })
  server: number[];

  @ApiProperty({ type: RatingFilter })
  rating: RatingFilter;

  @ApiProperty({ type: EpisodeFilter })
  episodes: EpisodeFilter;

  @ApiProperty({ type: NumberViewFilter })
  views: NumberViewFilter;
}
