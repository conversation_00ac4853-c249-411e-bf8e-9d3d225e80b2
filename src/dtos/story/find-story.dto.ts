import { ApiProperty } from '@nestjs/swagger';
import { RankingsType } from '../../shared/enums/rankings-type.enum';

export class FindStoryHotQueryDto {
  @ApiProperty({
    description: 'Loại rankings muốn lấy',
    enum: RankingsType,
    examples: {
      views_daily: {
        summary: 'views_daily',
        description: '<PERSON><PERSON>y theo lượt view daily',
      },
      views_monthly: {
        summary: 'views_monthly',
        description: 'Lấy theo lượ<PERSON> view tháng',
      },
      views_weekly: {
        summary: 'views_weekly',
        description: '<PERSON><PERSON>y theo lượ<PERSON> view tuần',
      },
      views_total: {
        summary: 'views_total',
        description: '<PERSON><PERSON>y theo tổng lượt view',
      },
    },
  })
  type: RankingsType;

  @ApiProperty({
    name: 'per_page',
    required: true,
    type: Number,
    description: 'Số lượng mỗi trang',
  })
  per_page: number;

  @ApiProperty({
    name: 'page',
    required: true,
    type: Number,
    description: 'Số trang',
  })
  page: number;
}
