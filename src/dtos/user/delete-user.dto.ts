import { ApiProperty } from '@nestjs/swagger';

export class DeletedUserInfoDto {
  @ApiProperty({
    description: 'ID của user',
    example: 123,
  })
  ID: bigint;

  @ApiProperty({
    description: 'User login đã được cập nhật với suffix _delete',
    example: 'john_doe_delete_1626789123456',
  })
  user_login: string;

  @ApiProperty({
    description: 'User nicename đã được cập nhật với suffix _delete',
    example: 'john_doe_delete_1626789123456',
  })
  user_nicename: string;

  @ApiProperty({
    description: 'Email đã được cập nhật với suffix _delete',
    example: 'john@example.com_delete_1626789123456',
  })
  user_email: string;

  @ApiProperty({
    description: 'Display name đã được cập nhật với suffix _delete',
    example: '<PERSON>_delete_1626789123456',
  })
  display_name: string;

  @ApiProperty({
    description: 'User status (2 = deleted)',
    example: 2,
  })
  user_status: number;
}

export class DeleteUserResponseDto {
  @ApiProperty({
    description: 'Thông báo kết quả soft delete user',
    example: 'User with ID 123 has been successfully marked as deleted',
  })
  message: string;
}
