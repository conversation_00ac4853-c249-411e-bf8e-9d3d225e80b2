import { ApiProperty } from '@nestjs/swagger';
import { TemplateType } from '../../shared/enums/template-type.enum';

export class TemplateConditionQueryDto {
  @ApiProperty({
    description: 'Loại template muốn lấy',
    enum: TemplateType,
    examples: {
      views_daily: {
        summary: 'overview',
        description: 'Tổng quan',
      },
      views_monthly: {
        summary: 'help',
        description: 'Lấy trang hỗ trợ',
      },
      views_weekly: {
        summary: 'thank_you_after_register',
        description: 'Trang cảm ơn sau khi đăng ký',
      },
    },
  })
  type: TemplateType;
}
