import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString } from 'class-validator';

export class UpdateUserDto {
  @IsString()
  @IsEmail()
  @ApiProperty()
  email: string;

  @IsString()
  @ApiProperty()
  first_name: string;

  @IsString()
  @ApiProperty()
  last_name: string;

  @IsString()
  @ApiProperty()
  old_password: string;

  @IsString()
  @ApiProperty()
  new_password: string;

  @IsString()
  @ApiProperty()
  banner_url: string;

  @IsString()
  @ApiProperty()
  avatar_url: string;
}
