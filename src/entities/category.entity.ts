import { ApiProperty } from '@nestjs/swagger';

export class CategoryEntity {
  @ApiProperty({ description: 'Tên loại' })
  name: string;
  @ApiProperty({ description: 'slug' })
  slug: string;
  @ApiProperty({ description: 'ID của category' })
  term_id: number;
  @ApiProperty({ description: 'Mô tả của category' })
  description: string;
  @ApiProperty({ description: 'Số lượng truyện của của loại' })
  count: number;

  constructor(partial: Partial<CategoryEntity>) {
    for (const key in partial) {
      if (typeof partial[key] === 'bigint') {
        partial[key] = Number(partial[key]);
      }
    }
    Object.assign(this, partial);
  }
}
