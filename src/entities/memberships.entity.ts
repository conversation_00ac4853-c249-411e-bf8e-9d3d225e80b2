import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';

export class MembershipsEntity extends BaseEntity {
  @ApiProperty({ description: 'ID của memberships' })
  id: number;
  @ApiProperty({ description: 'Tên' })
  name: string;
  @ApiProperty({ description: 'Label' })
  label: string;
  @ApiProperty({ description: 'Mô tả ngắn' })
  short_description: string;
  @ApiProperty({ description: 'Giá' })
  price: number;
  @ApiProperty({ description: 'Order' })
  the_order: number;
  @ApiProperty({ description: 'Mô tả' })
  description: string;
  @ApiProperty({ description: 'price_text' })
  price_text: string;
  @ApiProperty({ description: 'Role của User tương ứng' })
  custom_role_level: string;
  @ApiProperty({ description: 'Đơn vị tiền tệ' })
  currency: string;

  constructor(partial: Partial<MembershipsEntity>) {
    super(partial);
  }
}
