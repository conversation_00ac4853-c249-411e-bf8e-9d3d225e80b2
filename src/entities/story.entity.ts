import { ApiProperty } from '@nestjs/swagger';
import { LinkStoryInterface } from 'src/interfaces/link-story.interface';
import { BaseEntity } from './base.entity';

export class StoryEntity extends BaseEntity {
  @ApiProperty({ description: '<PERSON><PERSON>y tạo' })
  post_date_gmt: string;
  @ApiProperty({ description: 'Mô tả' })
  post_content: string;
  @ApiProperty({ description: 'Thông tin tác giả' })
  post_author: string;
  @ApiProperty({ description: 'Tiêu đề' })
  post_title: number;
  @ApiProperty({ description: 'Link' })
  post_name: string;
  @ApiProperty({ description: 'Lượt xem trong ngày' })
  views_daily: number;
  @ApiProperty({ description: 'Lượt xem trong tháng' })
  views_monthly: number;
  @ApiProperty({ description: 'Tổng lượt xem' })
  views_total: number;
  @ApiProperty({ description: 'Tổng lượt xem trong tuần' })
  views_weekly: number;
  @ApiProperty({ description: '<PERSON><PERSON> tập' })
  so_tap: number;
  @ApiProperty({ description: 'Số vote' })
  _kksr_casts: number;
  @ApiProperty({ description: 'Trung bình vote' })
  _kksr_avg: number;

  @ApiProperty({ description: 'ID của truyện' })
  post_id: number;

  @ApiProperty({ description: 'User đã thêm truyện này vào tủ sách' })
  userFavorites: boolean;

  @ApiProperty({ description: 'User đã đánh giá truyện này' })
  userVoted: boolean;

  constructor(partial: Partial<StoryEntity>) {
    super(partial);
  }
}

export class LinksStoryEntity extends BaseEntity {
  @ApiProperty({ description: 'Link truyện ở server 1' })
  S1: LinkStoryInterface[]; // Adjust type to LinkStoryInterface[]

  @ApiProperty({ description: 'Link truyện ở server 2' })
  S2: LinkStoryInterface[]; // Adjust type to LinkStoryInterface[]

  // @ApiProperty({ description: 'Link truyện ở server 2' })
  // servers_2: LinkStoryInterface[]; // Adjust type to LinkStoryInterface[]

  @ApiProperty({ description: 'Link truyện ở server VIP' })
  Svip: LinkStoryInterface[];

  constructor(partial: Partial<LinksStoryEntity>) {
    super(partial);
  }
}

export class BackgroundMusicEntity extends BaseEntity {
  @ApiProperty({ description: 'Tên' })
  name: string;
  @ApiProperty({ description: 'Tiêu đề' })
  title: string;
  @ApiProperty({ description: 'Link' })
  link: string;

  constructor(partial: Partial<BackgroundMusicEntity>) {
    super(partial);
  }
}
