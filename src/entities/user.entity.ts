import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';

export class UserEntity extends BaseEntity {
  @ApiProperty()
  ID: bigint;

  @ApiProperty()
  user_login: string;

  @ApiProperty()
  user_nicename: string;

  @ApiProperty()
  user_email: string;

  @ApiProperty()
  user_url: string;

  @ApiProperty()
  user_registered: Date;

  @ApiProperty()
  user_activation_key: string;

  @ApiProperty()
  user_status: number;

  @ApiProperty()
  display_name: string;

  constructor(partial: Partial<UserEntity>) {
    super(partial);
  }
}
