import { MailerService } from '@nestjs-modules/mailer';
import { Injectable } from '@nestjs/common';

@Injectable()
export class MailService {
  constructor(private mailerService: MailerService) {}

  async sendUserRequestResetPassword(
    deep_link: string,
    username: string,
    email_to: string,
    blog_name: string,
  ) {
    const url = deep_link;

    return await this.mailerService.sendMail({
      to: email_to,
      subject: `[${blog_name}] Thiết lập lại mật khẩu`,
      template: './confirmation',
      context: {
        username: username,
        url,
      },
    });
  }

  async sendUserBankTransfer(
    username: string,
    email_to: string,
    blog_name: string = 'AUDIO TRUYỆN FULL',
    content: string,
    subject: string,
    current_level: string,
    amount: string,
  ) {
    const replacedSubject = subject
      .replace('{blogname}', blog_name)
      .replace('{current_level}', current_level);

    let replacedContent = content
      .replaceAll('{blogname}', blog_name)
      .replaceAll('{username}', username)
      .replaceAll('{amount}', amount)
      .replaceAll('{currency}', 'VND')
      .replaceAll('{current_level}', current_level)
      .replaceAll('\n', '<br>');
    // Thay thế các ký tự xuống dòng đơn bằng thẻ <br>
    replacedContent = replacedContent.replace(/(?:\r\n|\r|\n)/g, '<br>');

    // Loại bỏ tất cả các dòng chỉ chứa thẻ <br>
    replacedContent = replacedContent.replace(/(<br>\s*)+/g, '<br>');

    // Loại bỏ các thẻ <br> trước và sau thẻ <hr>
    replacedContent = replacedContent.replace(/<br>\s*<hr>\s*<br>/g, '<hr>');

    // Loại bỏ các thẻ <br> trước thẻ <div>
    replacedContent = replacedContent.replace(/<br>\s*(<div)/g, '$1');

    return await this.mailerService.sendMail({
      to: email_to,
      subject: replacedSubject,
      html: replacedContent,
    });
  }
}
