import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  private logger = new Logger('HTTP');

  use(req: Request, res: Response, next: NextFunction): void {
    const { method, originalUrl, ip, headers, body, query, params } = req;
    const userAgent = headers['user-agent'] || '';
    const startTime = Date.now();

    res.on('finish', () => {
      const { statusCode } = res;
      const elapsedTime = Date.now() - startTime;
      this.logger.log(
        `Request: ${method} ${originalUrl}\n` +
          `Status: ${statusCode}\n` +
          `TimeResponse: ${elapsedTime}ms\n` +
          `Start: ${new Date(startTime).toISOString()}\n` +
          `End: ${new Date().toISOString()}\n` +
          `IP: ${ip}\n` +
          `User-Agent: ${userAgent}\n` +
          `Query: ${JSON.stringify(query)}\n` +
          `Params: ${JSON.stringify(params)}\n` +
          `Body: ${JSON.stringify(body)}`,
      );
    });

    next();
  }
}
