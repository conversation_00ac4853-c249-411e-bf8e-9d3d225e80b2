import {
  Body,
  Controller,
  Post,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ResponseFormatInterceptor } from 'src/decorators/response-format.decorator';
import { RegisterDto } from 'src/dtos/auth/register.dto';
import { ResetPasswordDto } from 'src/dtos/auth/reset-password.dto';
import { CustomRequest } from 'src/interfaces/custom-request.interface';
import { MailService } from 'src/mail/mail.service';
import { LoginDto } from '../../dtos/auth/login.dto';
import { RequestResetPasswordDto } from '../../dtos/auth/request-reset-password.dto';
import { AuthEntity } from '../../entities/auth.entity';
import { RegisterEntity } from '../../entities/register.entity';
import { AuthGuard } from './auth.guard';
import { AuthService } from './auth.service';
import { ConfigService } from '@nestjs/config';

@Controller('auth')
@ApiTags('auth')
@UseInterceptors(ResponseFormatInterceptor)
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly mailService: MailService,
    private readonly configService: ConfigService,
  ) {}

  @Post('login')
  @ApiOkResponse({ type: AuthEntity })
  login(@Body() { username, password }: LoginDto) {
    return this.authService.login(username, password);
  }

  @Post('register')
  @UseGuards(AuthGuard)
  @ApiOkResponse({ type: RegisterEntity })
  async register(
    @Body()
    { username, user_email, password, subscription_package }: RegisterDto,
    @Req() req: CustomRequest,
  ) {
    let userId = null;
    if (req.user) {
      userId = req.user.userId;
    }
    const result = await this.authService.register(
      username,
      user_email,
      password,
      subscription_package,
      userId,
    );

    const blog_name = await this.authService.getBlogName();

    const appVersionRelease = this.configService.get<string>(
      'APP_VERSION_RELEASE',
    );

    const headerVersion = req.headers['app-version'] as string;
    let disableFunctionPublishApp = parseInt(
      this.configService.get<string>('DISABLE_FUNCTION_PUBLISH_APP'),
    );
    if (headerVersion) {
      const v1 = headerVersion.split('.').map(Number);
      const v2 = appVersionRelease.split('.').map(Number);

      for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
        const num1 = v1[i] || 0; // Nếu không có thì mặc định là 0
        const num2 = v2[i] || 0;
        if (num1 > num2) {
          disableFunctionPublishApp = 1;
          break;
        }
      }
    }

    if (disableFunctionPublishApp === 0) {
      const requestSendEmail = await this.mailService.sendUserBankTransfer(
        username,
        user_email,
        blog_name,
        result.informationBanker.notifications_message,
        result.informationBanker.notifications_subject,
        result.informationBanker.memberships_label,
        result.informationBanker.price.toString(),
      );
      console.log(requestSendEmail);
    }

    return result.result;
  }
  @Post('reset-password')
  @ApiOkResponse({})
  async reset_password(
    @Body()
    { user_login, user_pass }: ResetPasswordDto,
  ) {
    const user = await this.authService.updatePassword(user_login, user_pass);
    return { user_email: user.user_email, user_login: user.user_login };
  }

  @Post('request/reset-password')
  @ApiOkResponse({})
  async request_reset_password(
    @Body()
    { user_login, deep_link }: RequestResetPasswordDto,
  ) {
    const user = await this.authService.findByUserLogin(user_login);
    const blog_name = await this.authService.getBlogName();
    const user_email = user.user_email;
    const requestSendEmail =
      await this.mailService.sendUserRequestResetPassword(
        deep_link,
        user_login,
        user_email,
        blog_name,
      );
    console.log(requestSendEmail);

    return {};
  }
}
