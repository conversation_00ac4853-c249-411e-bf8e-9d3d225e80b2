import {
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { RedisClientType } from 'redis';
import { HelperUtils } from 'src/shared/helpers/utils.helper';
import { CheckPassword, HashPassword } from 'wordpress-hash-node';
import { AuthEntity } from '../../entities/auth.entity';
import { PrismaService } from './../../prisma/prisma.service';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    @Inject('REDIS_CLIENT') private readonly redisClient: RedisClientType,
  ) {}

  async login(username: string, password: string): Promise<AuthEntity> {
    const user = await this.prisma.wpva_users.findFirst({
      where: {
        OR: [
          {
            user_login: username,
          },
          {
            user_email: username,
          },
        ],
      },
    });

    if (!user) {
      throw new NotFoundException(`Invalid Username or Password`);
    }
    const isPasswordValid = CheckPassword(password, user.user_pass);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid Username or Password');
    }

    const userId = user.ID;
    const userMetaValueCapabilities = await this.prisma.wpva_usermeta.findFirst(
      {
        where: {
          user_id: userId,
          meta_key: 'wpva_capabilities',
        },
        select: {
          meta_value: true,
        },
      },
    );
    let role = '';
    if (userMetaValueCapabilities) {
      role =
        HelperUtils.getRoleByWpCapabilities(
          userMetaValueCapabilities.meta_value,
        )?.[0] ?? '';
    }
    const accessToken = this.jwtService.sign({
      userLogin: user.user_login,
      userId: Number(user.ID),
      userEmail: user.user_email,
      userDisplay: user.display_name,
    });
    await this.redisClient.set(`token:${userId}`, accessToken, {
      EX: 172800,
    });

    return {
      accessToken: accessToken,
      role: role,
    };
  }

  generateOrderCode(orderId: number): string {
    const orderIdStr = orderId.toString();
    const paddingLength = Math.max(0, 6 - orderIdStr.length);
    const paddedOrderId = '0'.repeat(paddingLength) + orderIdStr;
    return `HVATF${paddedOrderId}`;
  }

  async register(
    username,
    user_email,
    password,
    subscription_package,
    userReqId?: string,
  ) {
    let userId = null;
    let newUser = null;
    if (!userReqId) {
      console.log('Start create user');
      const user = await this.prisma.wpva_users.findFirst({
        where: {
          user_login: username,
        },
        select: {
          user_login: true,
        },
      });

      if (user) {
        throw new NotFoundException(
          `Thông tin user_login ${username} đã tồn tại!`,
        );
      }

      const passwordHash = HashPassword(password);
      newUser = await this.prisma.wpva_users.create({
        data: {
          user_login: username,
          user_email: user_email,
          user_nicename: username,
          user_pass: passwordHash,
          display_name: username,
          user_url: '',
          user_activation_key: '',
        },
        select: {
          ID: true,
          user_login: true,
          user_email: true,
          user_nicename: true,
          display_name: true,
          user_pass: true,
          user_url: true,
          user_activation_key: true,
        },
      });

      console.log('Start create order');
      userId = Number(newUser.ID);

      const userMeta = await this.prisma.wpva_usermeta.createMany({
        data: [
          {
            user_id: userId,
            meta_key: 'admin_color',
            meta_value: 'fresh',
          },
          {
            user_id: userId,
            meta_key: 'comment_shortcuts',
            meta_value: 'false',
          },
          {
            user_id: userId,
            meta_key: 'confirm_email',
            meta_value: user_email,
          },
          {
            user_id: userId,
            meta_key: 'description',
            meta_value: '',
          },
          {
            user_id: userId,
            meta_key: 'dismissed_wp_pointers',
            meta_value: '',
          },
          {
            user_id: userId,
            meta_key: 'first_name',
            meta_value: '',
          },
          {
            user_id: userId,
            meta_key: 'ihc_locale_code',
            meta_value: 'vi',
          },
          {
            user_id: userId,
            meta_key: 'last_name',
            meta_value: '',
          },
          {
            user_id: userId,
            meta_key: 'locale',
            meta_value: '',
          },
          {
            user_id: userId,
            meta_key: 'nickname',
            meta_value: username,
          },
          {
            user_id: userId,
            meta_key: 'rich_editing',
            meta_value: 'true',
          },
          {
            user_id: userId,
            meta_key: 'show_admin_bar_front',
            meta_value: 'true',
          },
          {
            user_id: userId,
            meta_key: 'syntax_highlighting',
            meta_value: 'true',
          },
          {
            user_id: userId,
            meta_key: 'use_ssl',
            meta_value: '0',
          },
          {
            user_id: userId,
            meta_key: 'wpva_capabilities',
            meta_value: 'a:1:{s:10:"subscriber";b:1;}',
          },
          {
            user_id: userId,
            meta_key: 'wpva_user_level',
            meta_value: '0',
          },
        ],
      });
    } else {
      userId = Number(userReqId);
      newUser = await this.prisma.wpva_users.findFirst({
        where: {
          ID: userId,
        },
        select: {
          ID: true,
          user_login: true,
          user_email: true,
          user_nicename: true,
          display_name: true,
          user_pass: true,
          user_url: true,
          user_activation_key: true,
        },
      });
    }
    const informationBanker =
      await this.getInformationSendEmailBanker(subscription_package);

    const createDate = new Date();
    createDate.setHours(createDate.getHours() + 7);

    const order = await this.prisma.wpva_ihc_orders.create({
      data: {
        uid: Number(userId),
        status: 'pending',
        amount_type: 'VND',
        amount_value: informationBanker.price,
        automated_payment: false,
        create_date: createDate,
        lid: subscription_package,
      },
      select: {
        id: true,
      },
    });
    const orderId = order.id;
    const orderMeta = await this.prisma.wpva_ihc_orders_meta.createMany({
      data: [
        {
          order_id: orderId,
          meta_key: 'ihc_payment_type',
          meta_value: 'bank_transfer',
        },
        {
          order_id: orderId,
          meta_key: 'code',
          meta_value: this.generateOrderCode(orderId),
        },
        {
          order_id: orderId,
          meta_key: 'uid',
          meta_value: String(userId),
        },
        {
          order_id: orderId,
          meta_key: 'customer_email',
          meta_value: user_email,
        },
        {
          order_id: orderId,
          meta_key: 'customer_name',
          meta_value: username,
        },
        {
          order_id: orderId,
          meta_key: 'lid',
          meta_value: String(subscription_package),
        },
        {
          order_id: orderId,
          meta_key: 'level_description',
          meta_value: informationBanker.short_description,
        },
        {
          order_id: orderId,
          meta_key: 'level_label',
          meta_value: informationBanker.memberships_label,
        },
        {
          order_id: orderId,
          meta_key: 'initial_price',
          meta_value: String(informationBanker.price),
        },
        {
          order_id: orderId,
          meta_key: 'amount',
          meta_value: String(Number(informationBanker.price)),
        },
        {
          order_id: orderId,
          meta_key: 'base_price',
          meta_value: String(Number(informationBanker.price)),
        },
        {
          order_id: orderId,
          meta_key: 'currency',
          meta_value: 'VND',
        },
        {
          order_id: orderId,
          meta_key: 'taxes',
          meta_value: '0',
        },
        {
          order_id: orderId,
          meta_key: 'dynamic_price',
          meta_value: String(informationBanker.price),
        },
        {
          order_id: orderId,
          meta_key: 'dynamic_price_used',
          meta_value: '',
        },
        {
          order_id: orderId,
          meta_key: 'coupon_used',
          meta_value: '',
        },
        {
          order_id: orderId,
          meta_key: 'is_recurring',
          meta_value: informationBanker.access_limited_time_value,
        },
        {
          order_id: orderId,
          meta_key: 'interval_value',
          meta_value: informationBanker.access_regular_time_value,
        },
        {
          order_id: orderId,
          meta_key: 'interval_type',
          meta_value: informationBanker.access_regular_time_type,
        },
        {
          order_id: orderId,
          meta_key: 'key',
          meta_value: '',
        },
        {
          order_id: orderId,
          meta_key: 'is_parent_order',
          meta_value: '1',
        },
        {
          order_id: orderId,
          meta_key: 'order_identificator',
          meta_value: `${userId}_${subscription_package}_${this.convertTimestamp()}`,
        },
        {
          order_id: orderId,
          meta_key: 'subscription_cycles_limit',
          meta_value: '',
        },
      ],
    });

    // Bổ sung thêm logic check xem userLevel có tồn tại hay không?
    // Nếu tồn tại, thì update lại thời gian kết thúc và status.
    // Nếu không tồn tại, thì insert mới.
    const userLevel = await this.prisma.wpva_ihc_user_levels.findFirst({
      where: {
        user_id: userId,
        level_id: subscription_package,
      },
      select: {
        id: true,
      },
    });
    if (!userLevel) {
      const insertUserLevel = await this.prisma.$queryRaw`
      INSERT INTO wpva_ihc_user_levels (user_id, level_id, start_time, update_time, expire_time, notification, status)
      VALUES (${Number(userId)}, ${subscription_package}, ${createDate}, ${createDate}, '0000-00-00 00:00:00', false, 1)
      RETURNING id;
  `;
      console.log('insertUserLevel', insertUserLevel);
      const userLevelId = insertUserLevel[0].f0;
      const wpva_ihc_user_subscriptions_meta =
        await this.prisma.wpva_ihc_user_subscriptions_meta.createMany({
          data: [
            {
              subscription_id: userLevelId,
              meta_key: 'id',
              meta_value: String(subscription_package),
            },
            {
              subscription_id: userLevelId,
              meta_key: 'name',
              meta_value: informationBanker.memberships_name,
            },
            {
              subscription_id: userLevelId,
              meta_key: 'label',
              meta_value: informationBanker.memberships_label,
            },
            {
              subscription_id: userLevelId,
              meta_key: 'short_description',
              meta_value: informationBanker.short_description,
            },
            {
              subscription_id: userLevelId,
              meta_key: 'payment_type',
              meta_value: informationBanker.payment_type,
            },
            {
              subscription_id: userLevelId,
              meta_key: 'price',
              meta_value: String(informationBanker.price),
            },
            {
              subscription_id: userLevelId,
              meta_key: 'status',
              meta_value: '0',
            },
            {
              subscription_id: userLevelId,
              meta_key: 'the_order',
              meta_value: '0',
            },
            {
              subscription_id: userLevelId,
              meta_key: 'created_at',
              meta_value: String(this.getTimeStamp()),
            },
            {
              subscription_id: userLevelId,
              meta_key: 'recurring_cycles_count',
              meta_value: '0',
            },
          ],
        });
    }

    const result = {
      accessToken: this.jwtService.sign({ ...newUser, ID: Number(userId) }),
      order_code: this.generateOrderCode(orderId),
    };

    return {
      result: result,
      informationBanker: informationBanker,
    };
  }
  convertTimestamp() {
    const now = Date.now();
    const seconds = Math.floor(now / 1000);

    const milliseconds = now % 100000;

    const formattedTimestamp = `${seconds}_${milliseconds}`;

    return formattedTimestamp;
  }

  convertSeconds() {
    const now = Date.now();
    const seconds = Math.floor(now / 100000);

    const formattedTimestamp = `${seconds}`;

    return formattedTimestamp;
  }

  getTimeStamp() {
    const now = Date.now();
    const timestampInSeconds = Math.floor(now / 1000);
    return timestampInSeconds;
  }

  generateRandomString(length) {
    const characters = '0123456789abcdef';
    let result = '';
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      result += characters[randomIndex];
    }
    return result;
  }

  async getInformationSendEmailBanker(subscription_package) {
    const memberships = await this.prisma.wpva_ihc_memberships.findFirst({
      where: {
        id: subscription_package,
      },
      select: {
        label: true,
        price: true,
        short_description: true,
        payment_type: true,
        name: true,
      },
    });

    const notifications = await this.prisma.wpva_ihc_notifications.findFirst({
      where: {
        notification_type: 'bank_transfer',
      },
      select: {
        subject: true,
        message: true,
      },
    });
    const membershipMeta = await this.prisma.wpva_ihc_memberships_meta.findMany(
      {
        where: {
          membership_id: subscription_package,
          meta_key: {
            in: [
              'access_regular_time_type',
              'access_regular_time_value',
              'access_limited_time_value',
            ],
          },
        },
      },
    );
    const result = {
      memberships_label: memberships.label,
      memberships_name: memberships.name,
      price: memberships.price,
      short_description: memberships.short_description,
      payment_type: memberships.payment_type,
      notifications_subject: notifications.subject,
      notifications_message: notifications.message,
      access_regular_time_type: null,
      access_regular_time_value: null,
      access_limited_time_value: null,
    };
    membershipMeta.forEach((meta) => {
      if (meta.meta_key === 'access_regular_time_type') {
        result.access_regular_time_type = meta.meta_value;
      } else if (meta.meta_key === 'access_regular_time_value') {
        result.access_regular_time_value = meta.meta_value;
      } else if (meta.meta_key === 'access_limited_time_value') {
        result.access_limited_time_value = meta.meta_value;
      }
    });
    return result;
  }

  async updatePassword(user_login: string, password: string) {
    // Find the user by user_login
    const user = await this.prisma.wpva_users.findFirst({
      where: {
        user_login: user_login,
      },
      select: {
        ID: true, // Assuming 'ID' is the unique identifier
        user_login: true,
        user_email: true,
      },
    });

    // Throw an error if the user is not found
    if (!user) {
      throw new NotFoundException(`User login ${user_login} not found`);
    }

    try {
      // Hash the new password
      const passwordHash = await HashPassword(password);

      // Update the user's password using their unique ID
      const userUpdate = await this.prisma.wpva_users.update({
        where: {
          ID: user.ID,
        },
        data: {
          user_pass: passwordHash,
        },
      });

      // Return the updated user login and email
      return {
        user_login: userUpdate.user_login,
        user_email: userUpdate.user_email,
      };
    } catch (error) {
      // Handle any errors that occur during the update process
      throw new Error(`Failed to update password: ${error.message}`);
    }
  }

  async findByUserLogin(user_login) {
    const user = await this.prisma.wpva_users.findFirst({
      where: {
        user_login: user_login,
      },
      select: {
        user_login: true,
        user_email: true,
      },
    });
    if (!user) {
      throw new NotFoundException(`User login ${user_login} not found`);
    }

    return {
      user_login: user.user_login,
      user_email: user.user_email,
    };
  }
  async getBlogName() {
    const option_blog_name = await this.prisma.wpva_options.findFirst({
      where: {
        option_name: 'blogname',
      },
      select: {
        option_value: true,
      },
    });
    return option_blog_name.option_value ? option_blog_name.option_value : '';
  }
}
