import { Controller, Get, UseInterceptors } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ResponseFormatInterceptor } from 'src/decorators/response-format.decorator';
import { CategoryEntity } from 'src/entities/category.entity';
import { PaginatedResponse } from 'src/interfaces/response-format.interface';
import { CategoryService } from './category.service';

@Controller('categories')
@ApiTags('categories')
@UseInterceptors(ResponseFormatInterceptor)
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Get()
  @ApiOperation({
    summary: 'L<PERSON>y danh sách danh thể loại truyện',
  })
  @ApiOkResponse({ type: CategoryEntity, isArray: true })
  async findAll(): Promise<PaginatedResponse<CategoryEntity>> {
    const categories = await this.categoryService.findAll();
    return {
      items: categories,
    };
  }

  @Get('/type-of-story')
  @ApiOperation({
    summary: 'Lấy danh sách loại truyện',
  })
  @ApiOkResponse({ type: CategoryEntity, isArray: true })
  async findAllTypeOfStory(): Promise<PaginatedResponse<CategoryEntity>> {
    const arrTypeOfStory = await this.categoryService.findAllTypeOfStory();
    return {
      items: arrTypeOfStory,
    };
  }
  @Get('/status')
  @ApiOperation({
    summary: 'Lấy danh sách trạng thái',
  })
  @ApiOkResponse({ type: CategoryEntity, isArray: true })
  async findAllStatus(): Promise<PaginatedResponse<CategoryEntity>> {
    const arrStatus = await this.categoryService.findAllStatus();
    return {
      items: arrStatus,
    };
  }
  @Get('/server')
  @ApiOperation({
    summary: 'Lấy danh sách server',
  })
  @ApiOkResponse({ type: CategoryEntity, isArray: true })
  async findAllServer(): Promise<PaginatedResponse<CategoryEntity>> {
    const arrServer = await this.categoryService.findAllServer();
    return {
      items: arrServer,
    };
  }
  @Get('/number-view-min-max')
  @ApiOperation({
    summary: 'Lấy min, max số lượt view',
  })
  @ApiOkResponse({})
  async getMinMaxNumberViews() {
    return this.categoryService.getMinMaxNumberView();
  }
  @Get('/number-rating-min-max')
  @ApiOperation({
    summary: 'Lấy min, max số lượt rating',
  })
  @ApiOkResponse({})
  async getMinMaxNumberRating() {
    return this.categoryService.getMinMaxRating();
  }
  @Get('/number-chap-min-max')
  @ApiOperation({
    summary: 'Lấy min, max số tập',
  })
  @ApiOkResponse({})
  async getMinMaxNumberChap() {
    return this.categoryService.getMinMaxNumberChap();
  }
  @Get('/memberships')
  @ApiOperation({
    summary: 'Lấy thông tin gói',
  })
  @ApiOkResponse({})
  async getMemberships() {
    return this.categoryService.getMemberships();
  }
}
