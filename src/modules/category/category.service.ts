import { Inject, Injectable, Logger } from '@nestjs/common';
import { RedisClientType } from 'redis';
import { CategoryEntity } from 'src/entities/category.entity';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class CategoryService {
  constructor(
    private prisma: PrismaService,
    @Inject('REDIS_CLIENT') private readonly redisClient: RedisClientType,
  ) {}
  private logger = new Logger('CategoryService');
  private async getFromCacheOrDB<T>(
    cacheKey: string,
    ttl: number,
    fetchFunction: () => Promise<T>,
  ): Promise<T> {
    try {
      const cachedData = await this.redisClient.get(cacheKey);
      if (cachedData) {
        this.logger.log(`Cache hit for key: ${cacheKey}`);
        return JSON.parse(cachedData) as T;
      }

      this.logger.log(`Cache miss for key: ${cacheKey}. Fetching from DB...`);
      const data = await fetchFunction();
      await this.redisClient.set(cacheKey, JSON.stringify(data), {
        EX: ttl,
      });
      this.logger.log(`Data cached with key: ${cacheKey} for ${ttl} seconds`);
      return data;
    } catch (error) {
      this.logger.error(
        `Error fetching data for key: ${cacheKey}`,
        error.stack,
      );
      // Nếu có lỗi với Redis, vẫn tiếp tục fetch từ DB
      return fetchFunction();
    }
  }

  findAll(): Promise<CategoryEntity[]> {
    const cacheKey = 'category:findAll';
    const ttl = 3600; // Thời gian hết hạn cache: 1 giờ
    return this.getFromCacheOrDB<CategoryEntity[]>(cacheKey, ttl, async () => {
      const categories = await this.prisma.$queryRaw`
        SELECT wpva_terms.term_id, wpva_terms.name, wpva_terms.slug, wpva_term_taxonomy.description, wpva_term_taxonomy.count
        FROM wpva_terms
        JOIN wpva_term_taxonomy ON wpva_terms.term_id = wpva_term_taxonomy.term_id
        WHERE wpva_term_taxonomy.taxonomy = 'category' and wpva_term_taxonomy.parent != 0
        ;
      `;
      const results = categories as any[];

      return results.map(
        (item) =>
          new CategoryEntity({
            ...item,
          }),
      );
    });
  }

  findAllTypeOfStory(): Promise<CategoryEntity[]> {
    const cacheKey = 'category:findAllTypeOfStory';
    const ttl = 3600; // 1 giờ
    return this.getFromCacheOrDB<CategoryEntity[]>(cacheKey, ttl, async () => {
      const allTypeOfStory = await this.prisma.$queryRaw`
        SELECT wpva_terms.term_id, wpva_terms.name, wpva_terms.slug, wpva_term_taxonomy.description, wpva_term_taxonomy.count
        FROM wpva_terms
        JOIN wpva_term_taxonomy ON wpva_terms.term_id = wpva_term_taxonomy.term_id
        WHERE wpva_term_taxonomy.taxonomy = 'loai_truyen'
        ;
      `;
      const results = allTypeOfStory as any[];

      return results.map(
        (item) =>
          new CategoryEntity({
            ...item,
          }),
      );
    });
  }
  findAllStatus(): Promise<CategoryEntity[]> {
    const cacheKey = 'category:findAllStatus';
    const ttl = 3600; // 1 giờ
    return this.getFromCacheOrDB<CategoryEntity[]>(cacheKey, ttl, async () => {
      const allStatus = await this.prisma.$queryRaw`
        SELECT wpva_terms.term_id, wpva_terms.name, wpva_terms.slug, wpva_term_taxonomy.description, wpva_term_taxonomy.count
        FROM wpva_terms
        JOIN wpva_term_taxonomy ON wpva_terms.term_id = wpva_term_taxonomy.term_id
        WHERE wpva_term_taxonomy.taxonomy = 'trang_thai'
        ;
      `;
      const results = allStatus as any[];

      return results.map(
        (item) =>
          new CategoryEntity({
            ...item,
          }),
      );
    });
  }
  findAllServer(): Promise<CategoryEntity[]> {
    const cacheKey = 'category:findAllServer';
    const ttl = 3600; // 1 giờ

    return this.getFromCacheOrDB<CategoryEntity[]>(cacheKey, ttl, async () => {
      const allServer = await this.prisma.$queryRaw`
          SELECT wpva_terms.term_id, wpva_terms.name, wpva_terms.slug, wpva_term_taxonomy.description, wpva_term_taxonomy.count
          FROM wpva_terms
          JOIN wpva_term_taxonomy ON wpva_terms.term_id = wpva_term_taxonomy.term_id
          WHERE wpva_term_taxonomy.taxonomy = 'server'
          ;
        `;
      const arrServer = allServer as any[];

      return arrServer.map(
        (server) =>
          new CategoryEntity({
            ...server,
          }),
      );
    });
  }

  async getMinMaxSoTap() {
    const cacheKey = 'category:getMinMaxSoTap';
    const ttl = 3600; // 1 giờ
    return this.getFromCacheOrDB<{ min_value: number; max_value: number }>(
      cacheKey,
      ttl,
      async () => {
        const result = await this.prisma.wpva_postmeta.aggregate({
          _min: {
            meta_value: true,
          },
          _max: {
            meta_value: true,
          },
          where: {
            meta_key: 'so_tap',
          },
        });
        return {
          min_value: result._min.meta_value
            ? parseInt(result._min.meta_value, 10)
            : 0,
          max_value: result._max.meta_value
            ? parseInt(result._max.meta_value, 10)
            : 0,
        };
      },
    );
  }
  async getMinMaxRating() {
    const cacheKey = 'category:getMinMaxRating';
    const ttl = 3600; // 1 giờ
    return this.getFromCacheOrDB<{ min_value: number; max_value: number }>(
      cacheKey,
      ttl,
      async () => {
        const result = await this.prisma.wpva_postmeta.aggregate({
          _min: {
            meta_value: true,
          },
          _max: {
            meta_value: true,
          },
          where: {
            meta_key: '_kksr_avg',
          },
        });
        const min_value = result._min.meta_value
          ? parseInt(result._min.meta_value, 10)
          : 1;
        const max_value = result._max.meta_value
          ? parseInt(result._max.meta_value, 10)
          : 0;
        return {
          min_value: min_value,
          max_value: max_value + 1,
        };
      },
    );
  }
  async getMinMaxNumberView() {
    const cacheKey = 'category:getMinMaxNumberView';
    const ttl = 1800; // 30p
    return this.getFromCacheOrDB<{ min_value: number; max_value: number }>(
      cacheKey,
      ttl,
      async () => {
        const result = await this.prisma.$queryRaw`
          SELECT
            MIN(CAST(meta_value AS UNSIGNED)) AS min_meta_value,
            MAX(CAST(meta_value AS UNSIGNED)) AS max_meta_value
          FROM wpva_postmeta
          WHERE meta_key = 'views_total'
        `;

        return {
          min_value: Number(result[0].min_meta_value),
          max_value: Number(result[0].max_meta_value),
        };
      },
    );
  }
  async getMinMaxNumberChap() {
    const cacheKey = 'category:getMinMaxNumberChap';
    const ttl = 1800; // 1 giờ

    return this.getFromCacheOrDB<{ min_value: number; max_value: number }>(
      cacheKey,
      ttl,
      async () => {
        const result = await this.prisma.$queryRaw`
          SELECT
            MIN(CAST(meta_value AS UNSIGNED)) AS min_meta_value,
            MAX(CAST(meta_value AS UNSIGNED)) AS max_meta_value
          FROM wpva_postmeta
          WHERE meta_key = 'so_tap'
        `;

        return {
          min_value: Number(result[0].min_meta_value),
          max_value: Number(result[0].max_meta_value),
        };
      },
    );
  }
  async getMemberships() {
    const cacheKey = 'category:getMemberships';
    const ttl = 3600; // 1 giờ

    return this.getFromCacheOrDB<any[]>(cacheKey, ttl, async () => {
      const memberships = await this.prisma.wpva_ihc_memberships.findMany({
        where: {},
        select: {
          id: true,
          name: true,
          label: true,
          short_description: true,
          price: true,
          the_order: true,
        },
      });

      const memberships_meta =
        await this.prisma.wpva_ihc_memberships_meta.findMany({
          where: {
            meta_value: { not: null },
            meta_key: {
              in: ['description', 'price_text', 'custom_role_level', 'show_on'],
            },
          },
          select: {
            membership_id: true,
            meta_key: true,
            meta_value: true,
          },
        });

      const result_memberships_meta: {
        [key: string]: { [key: string]: any };
      } = {};

      memberships_meta.forEach((meta) => {
        const { membership_id, meta_key, meta_value } = meta;

        if (!result_memberships_meta[membership_id]) {
          result_memberships_meta[membership_id] = {};
        }

        result_memberships_meta[membership_id][meta_key] = meta_value;
      });

      const result = memberships
        .filter(
          (meta) =>
            result_memberships_meta[meta.id] &&
            result_memberships_meta[meta.id]['show_on'] === '1',
        )
        .map((meta) => {
          const { id, name, label, short_description, price, the_order } = meta;

          return {
            id,
            name,
            label,
            short_description,
            price,
            the_order,
            ...result_memberships_meta[id],
          };
        });

      return result;
    });
  }
}
