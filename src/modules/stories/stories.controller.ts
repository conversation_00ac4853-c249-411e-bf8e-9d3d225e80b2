import {
  <PERSON>,
  Controller,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { ResponseFormatInterceptor } from 'src/decorators/response-format.decorator';
import { FindStoryConditionQueryDto } from 'src/dtos/story/find-story-condition.dto';
import { FindStoryHotQueryDto } from 'src/dtos/story/find-story.dto';
import { VoteDto } from 'src/dtos/story/vote.dto';
import {
  BackgroundMusicEntity,
  LinksStoryEntity,
  StoryEntity,
} from 'src/entities/story.entity';
import { CustomRequest } from 'src/interfaces/custom-request.interface';
import { PaginatedResponse } from 'src/interfaces/response-format.interface';
import { HelperUtils } from '../../shared/helpers/utils.helper';
import { AuthGuard } from '../auth/auth.guard';
import { StoryService } from './stories.service';

@Controller('stories')
@ApiTags('stories')
@ApiExtraModels(FindStoryHotQueryDto)
@UseInterceptors(ResponseFormatInterceptor)
export class StoryController {
  private logger = new Logger('StoryController');
  private mappingFieldOrder = {
    random: 'random',
    new: 'post_modified',
    views_weekly: 'views_weekly',
    views_daily: 'views_daily',
    views_total: 'views_total',
    views_monthly: 'views_monthly',
    so_tap: 'so_tap',
    _kksr_avg: '_kksr_avg',
    comments: 'comment_count',
    simplefavorites_count: 'simplefavorites_count',
    _kksr_casts: '_kksr_casts',
  };
  constructor(private readonly storyService: StoryService) {}

  @Get('')
  @ApiQuery({ name: 'search', type: typeof 'string', required: false })
  @ApiQuery({ name: 'per_page', required: true, type: Number })
  @ApiQuery({ name: 'page', required: true, type: Number })
  @ApiQuery({ name: 'order_by', type: typeof 'string' })
  @ApiQuery({ name: 'sort_by', type: typeof 'string' })
  @ApiQuery({ name: 'cate_ids', type: typeof 'string', required: false })
  @UseGuards(AuthGuard)
  @ApiOkResponse({ type: StoryEntity, isArray: true })
  async findAllStory(
    @Query('search') search: string,
    @Query('per_page') per_page: number,
    @Query('page') page: number,
    @Query('order_by') order_by?: string,
    @Query('sort_by') sort_by: string = 'desc',
    @Query('cate_ids') cate_ids?: string,
    @Req() req?: CustomRequest,
  ): Promise<PaginatedResponse<StoryEntity>> {
    // Tạo dictionary với object literal
    this.logger.log(
      `findAllStory :: by condition :: search :: ${search}, order_by :: ${order_by}, sort_by :: ${sort_by}`,
    );
    let orderField = this.mappingFieldOrder[order_by] || 'post_date';
    if (order_by === 'random') {
      orderField = HelperUtils.getRandomChoice([
        'post_title',
        'post_date',
        'views_daily',
        'views_monthly',
        'so_tap',
      ]);
    }

    console.log('orderField', orderField);
    if (!orderField) {
      orderField = 'post_date';
    }
    let userId = null;
    if (req.user) {
      userId = req.user.userId;
    }

    return await this.storyService.queryAllStory(
      per_page,
      page,
      search,
      orderField,
      sort_by,
      req,
      userId,
      cate_ids,
    );
  }
  @Get('new-chapter')
  @UseGuards(AuthGuard)
  @ApiQuery({ name: 'search', type: typeof 'string', required: false })
  @ApiQuery({ name: 'per_page', required: true, type: Number })
  @ApiQuery({ name: 'page', required: true, type: Number })
  @ApiOkResponse({ type: StoryEntity, isArray: true })
  async findAllStoryNewChapter(
    @Query('search') search: string,
    @Query('per_page') per_page: number,
    @Query('page') page: number,
    @Req() req: CustomRequest,
  ): Promise<PaginatedResponse<StoryEntity>> {
    const orderField = 'post_modified';

    let userId = null;
    if (req.user) {
      userId = req.user.userId;
    }

    return await this.storyService.findAllStoryNewChapter(
      per_page,
      page,
      search,
      orderField,
      'desc',
      req,
      userId,
    );
  }
  @Get(':id/links-story')
  @ApiOkResponse({ type: LinksStoryEntity })
  async getLinksStory(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<LinksStoryEntity> {
    return await this.storyService.getLinksByStoryId(id);
  }
  @Get(':id/information')
  @ApiOkResponse({})
  @ApiOperation({
    summary:
      'Api lấy danh sách thông tin của story như tác giả, thể loại, loại truyện, trạng thái',
  })
  async getInformation(@Param('id', ParseIntPipe) id: number) {
    return this.storyService.getInformationByStoryID(id);
  }
  @Get('background-music')
  @ApiOperation({ summary: 'Lấy danh sách nhạc phát nền' })
  @ApiOkResponse({})
  async getBackgroundMusic(): Promise<BackgroundMusicEntity[]> {
    return this.storyService.getBackgroundMusic();
  }
  @Post(':post_id/vote')
  @ApiOperation({ summary: 'Api vote' })
  @ApiOkResponse({})
  async votePostId(
    @Param('post_id', ParseIntPipe) post_id: number,
    @Body() { type_vote, rating }: VoteDto,
    @Req() req?: Request,
  ) {
    return this.storyService.votePostId(req, post_id, type_vote, rating);
  }

  @Post('/actions/filter')
  @ApiQuery({ name: 'search', type: typeof 'string', required: false })
  @ApiQuery({ name: 'per_page', required: true, type: Number })
  @ApiQuery({ name: 'page', required: true, type: Number })
  @ApiQuery({ name: 'order_by', type: typeof 'string', required: false })
  @ApiQuery({ name: 'sort_by', type: typeof 'string', required: false })
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Danh sách truyện theo bộ lọc' })
  @ApiOkResponse({})
  async storiesActionsFilter(
    @Body() body: FindStoryConditionQueryDto,
    @Query('search') search: string,
    @Query('per_page') per_page: number,
    @Query('page') page: number,
    @Query('order_by') order_by: string = 'post_date',
    @Query('sort_by') sort_by: string = 'desc',

    @Req() req: CustomRequest,
  ) {
    let orderField = this.mappingFieldOrder[order_by] || 'post_date';

    if (order_by == 'random') {
      orderField = HelperUtils.getRandomChoice([
        'post_title',
        'post_date',
        'views_daily',
        'views_monthly',
        'so_tap',
      ]);
    }
    if (!orderField) {
      orderField = 'post_date';
    }

    let userId = null;
    if (req.user) {
      userId = req.user.userId;
    }

    return this.storyService.storiesActionsFilter(
      req,
      body,
      per_page,
      page,
      search,
      orderField,
      sort_by,
      userId,
    );
  }
  @Get('/slugs/:slug')
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Lấy chi tiết truyện theo slug' })
  @ApiOkResponse({})
  async detailPostBySlug(
    @Param('slug') slug: string,

    @Req() req: CustomRequest,
  ) {
    let userId = null;
    if (req.user) {
      userId = req.user.userId;
    }

    return this.storyService.getStoryBySlug(req, slug, userId);
  }
}
