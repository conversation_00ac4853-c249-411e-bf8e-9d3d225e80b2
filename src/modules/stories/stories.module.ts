//src/auth/auth.module.ts
import { Module } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { RedisModule } from 'src/caching/redis.caching';
import { PrismaModule } from 'src/prisma/prisma.module';
import { StoryController } from './stories.controller';
import { StoryService } from './stories.service';

export const jwtSecret = 'zjP9h6ZI5LoSKCRj';

@Module({
  imports: [PrismaModule, RedisModule],
  controllers: [StoryController],
  providers: [StoryService, JwtService],
  exports: [StoryService],
})
export class StoryModule {}
