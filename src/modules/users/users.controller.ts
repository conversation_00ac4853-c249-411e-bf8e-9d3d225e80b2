import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { ResponseFormatInterceptor } from 'src/decorators/response-format.decorator';
import { UpdateSimpleFavorites } from 'src/dtos/user/simple-favorites.dto';
import { TemplateConditionQueryDto } from 'src/dtos/user/template.dto';
import { UpdateUserDto } from 'src/dtos/user/update-user.dto';
import { DeleteUserResponseDto } from 'src/dtos/user/delete-user.dto';
import { UserEntity } from 'src/entities/user.entity';
import { UserService } from './users.service';

@Controller('users')
@ApiTags('users')
@UseInterceptors(ResponseFormatInterceptor)
export class UserController {
  constructor(private readonly userService: UserService) {}
  @Get('/setting')
  @UseGuards()
  @ApiOkResponse({})
  async getSetting(@Req() req: Request) {
    return await this.userService.getSetting(req);
  }

  @Get('/notifications')
  @UseGuards()
  @ApiOkResponse({})
  async notifications() {
    return await this.userService.notifications();
  }

  @Get(':id')
  @UseGuards()
  @ApiOkResponse({ type: UserEntity })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return new UserEntity(await this.userService.detailUserById(id, false));
  }
  @Put(':id')
  @UseGuards()
  @ApiOkResponse({})
  async updateUser(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateUserDto,
  ) {
    return await this.userService.updateUser(id, body);
  }

  @Get('/:id/simplefavorites')
  @UseGuards()
  @ApiOkResponse({})
  @ApiOperation({
    summary: 'Api lấy danh sách truyện trong tủ truyện',
  })
  async getSimpleFavorites(
    @Param('id', ParseIntPipe) id: number,
    @Req() req?: Request,
  ) {
    return await this.userService.getSimpleFavorites(req, id);
  }
  @Delete('/:id/simplefavorites')
  @UseGuards()
  @ApiQuery({ name: 'post_id', required: false, type: Number })
  @ApiOperation({
    summary: 'Api lấy xoá truyện trong tủ truyện',
  })
  async deleteSimpleFavorites(
    @Param('id', ParseIntPipe) id: number,
    @Query('post_id') post_id?: number,
    @Req() req?: Request,
  ) {
    return await this.userService.deleteSimpleFavorites(id, req, post_id);
  }

  @Post('/:id/simplefavorites')
  @UseGuards()
  @ApiOkResponse({})
  @ApiOperation({
    summary: 'Api thêm truyện vào tủ truyện',
  })
  async addSimpleFavorites(
    @Param('id', ParseIntPipe) id: number,
    @Body() { type, post_id }: UpdateSimpleFavorites,
    @Req() req?: Request,
  ) {
    return await this.userService.addSimpleFavorites(id, req, post_id, type);
  }
  @Get('/:id/purchased-packages')
  @UseGuards()
  @ApiOkResponse({})
  async getPurchasedPackages(@Param('id', ParseIntPipe) id: number) {
    return await this.userService.getPurchasedPackages(id);
  }
  @Get('/page/templates')
  @UseGuards()
  @ApiOkResponse({})
  async getPageTemplates(@Query() query: TemplateConditionQueryDto) {
    return await this.userService.getPageTemplates(query.type);
  }

  @Delete(':id')
  @UseGuards()
  @ApiOkResponse({ type: DeleteUserResponseDto })
  @ApiOperation({
    summary:
      'Api soft delete user - cập nhật thông tin user thêm suffix _delete',
    description:
      'API này không xóa user khỏi database mà chỉ cập nhật các trường thông tin để đánh dấu đã bị xóa',
  })
  async deleteUser(@Param('id', ParseIntPipe) id: number) {
    return await this.userService.deleteUser(id);
  }

  @Post(':id/restore')
  @UseGuards()
  @ApiOkResponse({})
  @ApiOperation({
    summary: 'Api khôi phục user đã bị soft delete',
    description:
      'API này khôi phục user bằng cách remove suffix _delete khỏi các trường thông tin',
  })
  async restoreUser(@Param('id', ParseIntPipe) id: number) {
    return await this.userService.restoreUser(id);
  }

  @Get('/notifications/details')
  @UseGuards()
  @ApiOkResponse({})
  async notificationsDetails() {
    return await this.userService.notifications();
  }
}
