import { Module } from '@nestjs/common';
import { PrismaModule } from 'src/prisma/prisma.module';
import { StoryModule } from '../stories/stories.module';
import { StoryService } from '../stories/stories.service';
import { UserController } from './users.controller';
import { UserService } from './users.service'; // Đảm bảo import UserService từ local module

@Module({
  imports: [PrismaModule, StoryModule], // Import PrismaModule và StoryModule
  controllers: [UserController], // Khai báo controllers nếu có
  providers: [UserService, StoryService],
  exports: [UserService], // Export UserService nếu cần sử dụng bên ngoài
})
export class UserModule {}
