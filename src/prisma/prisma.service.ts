import { Injectable, On<PERSON><PERSON>ule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService
  extends PrismaClient
  implements OnModuleInit, OnModuleDestroy
{
  private static instanceCount = 0;
  private instanceId: number;

  constructor() {
    super({
      log: [
        {
          emit: 'event',
          level: 'query',
        },
      ],
    });

    this.instanceId = ++PrismaService.instanceCount;
    console.log(`Created PrismaService instance #${this.instanceId}`);
  }

  async onModuleInit() {
    this.$on('query' as never, (e: any) => {
      console.log(`PrismaService #${this.instanceId} query: ${e.query}`);
    });

    await this.$connect();
  }

  async onModuleDestroy() {
    console.log(`Destroying PrismaService instance #${this.instanceId}...`);
    await this.$disconnect();
  }
}
