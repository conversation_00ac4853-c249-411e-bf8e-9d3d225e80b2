import { serialize, unserialize } from 'php-serialize';

export class HelperUtils {
  static getRandomChoice(arr: string[]): string {
    console.log('arr', arr);
    const randomIndex = Math.floor(Math.random() * arr.length);
    return arr[randomIndex];
  }

  static getRoleByWpCapabilities(serializedString: string): string[] {
    const regex = /s:\d+:"([^"]+)";b:\d;/g;
    const result: string[] = [];
    let match;

    while ((match = regex.exec(serializedString)) !== null) {
      const fieldName = match[1];
      result.push(fieldName);
    }

    return result;
  }

  static unserializeString(serializedString: string): any {
    try {
      // Unserialize chuỗi PHP
      const data = unserialize(serializedString);
      return data;
    } catch (error) {
      throw new Error('Failed to unserialize string');
    }
  }

  static serializeString(unSerialized: any): any {
    try {
      // Serialize chuỗi PHP
      const data = serialize(unSerialized);
      return data;
    } catch (error) {
      throw new Error('Failed to serializeString string');
    }
  }

  static getCurrentFormattedTime() {
    const now = new Date();
    return now;
  }
}
